# Italy Visa Application System

Система для подачи заявок на итальянскую визу с административной панелью.

## Особенности

- Форма заявки на основе JSON-схемы с валидацией
- Административная панель только для авторизованных пользователей
- Управление статусами заявок (Новая → Оплаченная → Завершенная)
- База данных PostgreSQL с Drizzle ORM и Prisma
- Docker-контейнеризация для микроархитектуры

## Стек технологий

- **Frontend**: Next.js 15, <PERSON><PERSON>, TypeScript, Tailwind CSS
- **Authentication**: NextAuth.js (email/phone login)
- **Database**: PostgreSQL (Neon.tech), Drizzle ORM, Prisma
- **Forms**: React Hook Form с Zod валидацией
- **Containerization**: Docker, Docker Compose

## Настройка

### 1. Клонирование и установка зависимостей

```bash
git clone <repository-url>
cd italyvisa
npm install
```

### 2. Настройка переменных окружения

Создайте файл `.env.local`:

```env
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here

# Neon.tech Database
DATABASE_URL="****************************************************************"

# Admin credentials
ADMIN_EMAIL=<EMAIL>
ADMIN_PHONE=+7777908990
ADMIN_PASSWORD=admin123
```

### 3. Настройка базы данных

```bash
# Генерация Prisma клиента
npx prisma generate

# Применение миграций Drizzle
npm run db:migrate

# Применение миграций Prisma (если нужно)
npx prisma db push
```

### 4. Запуск в режиме разработки

```bash
npm run dev
```

Приложение будет доступно по адресу `http://localhost:3000`

## Docker

### Локальный запуск с Docker Compose

```bash
# Запуск всех сервисов
docker-compose up --build

# Запуск в фоновом режиме
docker-compose up -d --build
```

Сервисы:
- **Приложение**: http://localhost:3000
- **PostgreSQL**: localhost:5432
- **Adminer**: http://localhost:8080 (для управления БД)

### Отдельная сборка Docker-образа

```bash
# Сборка образа
docker build -t italyvisa .

# Запуск контейнера
docker run -p 3000:3000 --env-file .env.local italyvisa
```

## Структура проекта

```
src/
├── app/                    # Next.js App Router
│   ├── admin/             # Админ панель
│   ├── auth/              # Страницы аутентификации
│   ├── api/               # API маршруты
│   └── ...
├── components/            # React компоненты
│   ├── VisaApplicationForm.tsx
│   └── AdminPanel.tsx
├── lib/                   # Утилиты и конфигурация
│   ├── auth.ts           # NextAuth конфигурация
│   ├── validation.ts     # Zod схемы
│   └── db/               # База данных
│       ├── schema.ts     # Drizzle схема
│       └── index.ts      # Подключение к БД
```

## Функциональность

### Форма заявки
- Личная информация (ФИО, дата рождения, пол)
- Паспортные данные
- Контактная информация
- Предпочтения по визе
- Дополнительные опции
- Согласия

### Администрирование
- Просмотр всех заявок в табличном виде
- Управление статусами:
  - **Новая** → кнопка "Оплачено"
  - **Оплаченная** → кнопка "Подать заявку" (передает JSON заявки)
  - **Завершенная** → кнопка "Отменить заявку"
- Отображение ошибок
- Сортировка по дате создания

### Аутентификация
- Вход только для администраторов
- Поддержка email и телефона
- Защищенные маршруты

## API Endpoints

- `POST /api/applications` - Создание заявки
- `GET /api/applications` - Получение всех заявок
- `PATCH /api/applications/[id]` - Обновление статуса
- `POST /api/applications/[id]` - Получение JSON для подачи

## Развертывание

1. Настройте базу данных Neon.tech
2. Обновите переменные окружения
3. Запустите `docker-compose up -d --build`
4. Приложение будет доступно на указанном порту

## Безопасность

- Все формы защищены валидацией
- Административные функции требуют аутентификации
- Конфиденциальные данные хранятся в переменных окружения
- Docker-контейнеры изолированы
