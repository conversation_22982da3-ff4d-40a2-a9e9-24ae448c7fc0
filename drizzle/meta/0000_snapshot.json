{"id": "60097fe5-810b-45b8-a497-f446c15a2ad2", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.visa_applications": {"name": "visa_applications", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "full_name": {"name": "full_name", "type": "text", "primaryKey": false, "notNull": true}, "date_of_birth": {"name": "date_of_birth", "type": "text", "primaryKey": false, "notNull": true}, "gender": {"name": "gender", "type": "text", "primaryKey": false, "notNull": true}, "passport_number": {"name": "passport_number", "type": "text", "primaryKey": false, "notNull": true}, "passport_expiry": {"name": "passport_expiry", "type": "text", "primaryKey": false, "notNull": true}, "nationality": {"name": "nationality", "type": "text", "primaryKey": false, "notNull": false, "default": "'KAZAKHSTAN'"}, "phone_number": {"name": "phone_number", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "visa_category": {"name": "visa_category", "type": "text", "primaryKey": false, "notNull": true}, "visa_subcategory": {"name": "visa_subcategory", "type": "text", "primaryKey": false, "notNull": true}, "preferred_centers": {"name": "preferred_centers", "type": "jsonb", "primaryKey": false, "notNull": true}, "date_flexibility": {"name": "date_flexibility", "type": "text", "primaryKey": false, "notNull": true}, "preferred_date_range": {"name": "preferred_date_range", "type": "jsonb", "primaryKey": false, "notNull": false}, "urgency": {"name": "urgency", "type": "text", "primaryKey": false, "notNull": true}, "alternative_options": {"name": "alternative_options", "type": "jsonb", "primaryKey": false, "notNull": false}, "comments": {"name": "comments", "type": "text", "primaryKey": false, "notNull": false}, "data_processing": {"name": "data_processing", "type": "text", "primaryKey": false, "notNull": true}, "terms_acceptance": {"name": "terms_acceptance", "type": "text", "primaryKey": false, "notNull": true}, "notification_consent": {"name": "notification_consent", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "application_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'Новая'"}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.application_status": {"name": "application_status", "schema": "public", "values": ["Новая", "Оплаченная", "Завершенная"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}