CREATE TYPE "public"."application_status" AS ENUM('Новая', 'Оплаченная', 'Завершенная');--> statement-breakpoint
CREATE TABLE "visa_applications" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"full_name" text NOT NULL,
	"date_of_birth" text NOT NULL,
	"gender" text NOT NULL,
	"passport_number" text NOT NULL,
	"passport_expiry" text NOT NULL,
	"nationality" text DEFAULT 'KAZAKHSTAN',
	"phone_number" text NOT NULL,
	"email" text NOT NULL,
	"visa_category" text NOT NULL,
	"visa_subcategory" text NOT NULL,
	"preferred_centers" jsonb NOT NULL,
	"date_flexibility" text NOT NULL,
	"preferred_date_range" jsonb,
	"urgency" text NOT NULL,
	"alternative_options" jsonb,
	"comments" text,
	"data_processing" text NOT NULL,
	"terms_acceptance" text NOT NULL,
	"notification_consent" text,
	"status" "application_status" DEFAULT 'Новая' NOT NULL,
	"error" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
