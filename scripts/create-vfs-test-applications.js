const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Updated Kazakhstan test data with VFS system requirements
const kazakhstanVFSTestData = [
  {
    // Individual Tourism Application
    isFamily: false,
    familySize: 1,
    fullName: "АЙДАР НАЗАРБАЕВ",
    dateOfBirth: "1985-03-15",
    gender: "Male",
    passportNumber: "N12345678",
    passportExpiry: "2027-03-15",
    nationality: "KAZAKHSTAN",
    phoneNumber: "+77012345678",
    email: "aidar.nazar<PERSON><EMAIL>",
    visaCategory: "TOURISM",
    visaSubcategory: "INDIVIDUAL",
    preferredCenters: ["ALMATY"],
    dateFlexibility: "any_date",
    comments: "Первая поездка в Италию для туризма",
    // VFS System Codes
    countryCode: "kaz",
    missionCode: "ita", 
    nationalityCode: "KAZ",
    languageCode: "ru-RU",
    dialCode: "7",
    vfsCenterCode: "ALA",
    vfsVisaCategoryCode: "TOU",
    vfsVisaSubcategoryCode: "VIS",
    applicantType: 0,
    applicantGroupId: 0,
    isEdit: false,
    isWaitlist: false,
    isAutoRefresh: true,
    amount: 17535,
    currencyCode: "KZT",
    paymentMode: "Vac",
    purposeId1: "5dac333a-b947-4f47-a1eb-4140bc27ff7c",
    purposeId2: "42bad10d-916e-401f-9197-68d757a4ee8a",
    roleName: "Individual",
    notificationType: "none"
  },
  {
    // Family Application with 2 adults
    isFamily: true,
    familySize: 2,
    fullName: "ГУЛЬНАРА ТОКАЕВА",
    dateOfBirth: "1990-07-22",
    gender: "Female",
    passportNumber: "N87654321",
    passportExpiry: "2028-07-22",
    nationality: "KAZAKHSTAN",
    phoneNumber: "+77025555555",
    email: "<EMAIL>",
    visaCategory: "FAMILY",
    visaSubcategory: "FAMILY_VISIT",
    preferredCenters: ["ASTANA", "ALMATY"],
    dateFlexibility: "date_range",
    preferredDateRange: {
      from: "2024-09-01",
      to: "2024-09-30"
    },
    applicants: [
      {
        firstName: "ГУЛЬНАРА",
        lastName: "ТОКАЕВА",
        dateOfBirth: "1990-07-22",
        gender: "Female",
        passportNumber: "N87654321",
        passportExpiry: "2028-07-22",
        nationality: "KAZAKHSTAN",
        isEndorsedChild: false,
        relationshipToMain: "main"
      },
      {
        firstName: "АРМАН",
        lastName: "ТОКАЕВ",
        dateOfBirth: "1988-05-10",
        gender: "Male",
        passportNumber: "N11223344", 
        passportExpiry: "2029-05-10",
        nationality: "KAZAKHSTAN",
        isEndorsedChild: false,
        relationshipToMain: "spouse"
      }
    ],
    comments: "Семейная поездка к родственникам в Милане",
    // VFS System Codes
    countryCode: "kaz",
    missionCode: "ita",
    nationalityCode: "KAZ", 
    languageCode: "ru-RU",
    dialCode: "7",
    vfsCenterCode: "AST",
    vfsVisaCategoryCode: "FAM",
    vfsVisaSubcategoryCode: "PV",
    applicantType: 0,
    applicantGroupId: 0,
    isEdit: false,
    isWaitlist: false,
    isAutoRefresh: true,
    amount: 35070, // 2 people x 17535
    currencyCode: "KZT",
    paymentMode: "Vac",
    purposeId1: "5dac333a-b947-4f47-a1eb-4140bc27ff7c",
    purposeId2: "42bad10d-916e-401f-9197-68d757a4ee8a",
    roleName: "Individual",
    notificationType: "none"
  },
  {
    // Family Application with children (VFS child requirements)
    isFamily: true,
    familySize: 4,
    fullName: "СЕРИК АБАЕВ",
    dateOfBirth: "1982-11-05",
    gender: "Male",
    passportNumber: "N55667788",
    passportExpiry: "2026-11-05",
    nationality: "KAZAKHSTAN",
    phoneNumber: "+77077777777",
    email: "<EMAIL>",
    visaCategory: "TOURISM",
    visaSubcategory: "GROUP",
    preferredCenters: ["SHYMKENT", "ALMATY"],
    dateFlexibility: "any_date",
    applicants: [
      {
        firstName: "СЕРИК",
        lastName: "АБАЕВ",
        dateOfBirth: "1982-11-05",
        gender: "Male",
        passportNumber: "N55667788",
        passportExpiry: "2026-11-05",
        nationality: "KAZAKHSTAN",
        isEndorsedChild: false,
        relationshipToMain: "main"
      },
      {
        firstName: "МАДИНА",
        lastName: "АБАЕВА",
        dateOfBirth: "1985-08-18",
        gender: "Female",
        passportNumber: "N99887766",
        passportExpiry: "2027-08-18", 
        nationality: "KAZAKHSTAN",
        isEndorsedChild: false,
        relationshipToMain: "spouse"
      },
      {
        firstName: "АЛИЯ",
        lastName: "АБАЕВА",
        dateOfBirth: "2015-04-12", // Child under 12 - requires parent passport
        gender: "Female",
        passportNumber: "N44556677",
        passportExpiry: "2025-04-12",
        nationality: "KAZAKHSTAN",
        isEndorsedChild: true,
        relationshipToMain: "child",
        parentPassportNumber: "N55667788",
        parentPassportExpiry: "2026-11-05"
      },
      {
        firstName: "ДАНИЯР",
        lastName: "АБАЕВ",
        dateOfBirth: "2018-09-30", // Child under 12 - requires parent passport
        gender: "Male",
        passportNumber: "N33445566",
        passportExpiry: "2028-09-30",
        nationality: "KAZAKHSTAN",
        isEndorsedChild: true,
        relationshipToMain: "child",
        parentPassportNumber: "N55667788",
        parentPassportExpiry: "2026-11-05"
      }
    ],
    comments: "Семейный отпуск в Риме и Флоренции с детьми",
    // VFS System Codes
    countryCode: "kaz",
    missionCode: "ita",
    nationalityCode: "KAZ",
    languageCode: "ru-RU",
    dialCode: "7",
    vfsCenterCode: "SHY",
    vfsVisaCategoryCode: "TOU",
    vfsVisaSubcategoryCode: "TOU",
    applicantType: 0,
    applicantGroupId: 0,
    isEdit: false,
    isWaitlist: false,
    isAutoRefresh: true,
    amount: 70140, // 4 people x 17535
    currencyCode: "KZT",
    paymentMode: "Vac",
    purposeId1: "5dac333a-b947-4f47-a1eb-4140bc27ff7c",
    purposeId2: "42bad10d-916e-401f-9197-68d757a4ee8a",
    roleName: "Individual",
    notificationType: "none"
  },
  {
    // Business Application
    isFamily: false,
    familySize: 1,
    fullName: "ЖАНАТ МУСТАФИН", 
    dateOfBirth: "1975-12-08",
    gender: "Male",
    passportNumber: "N98765432",
    passportExpiry: "2025-12-08",
    nationality: "KAZAKHSTAN",
    phoneNumber: "+***********",
    email: "<EMAIL>",
    visaCategory: "BUSINESS",
    visaSubcategory: "BUSINESS_MEETING",
    preferredCenters: ["ALMATY"],
    dateFlexibility: "date_range",
    preferredDateRange: {
      from: "2024-10-15",
      to: "2024-10-25"
    },
    comments: "Деловая поездка для участия в международной конференции",
    // VFS System Codes
    countryCode: "kaz",
    missionCode: "ita",
    nationalityCode: "KAZ",
    languageCode: "ru-RU",
    dialCode: "7",
    vfsCenterCode: "ALA",
    vfsVisaCategoryCode: "BUS",
    vfsVisaSubcategoryCode: "BUS",
    applicantType: 0,
    applicantGroupId: 0,
    isEdit: false,
    isWaitlist: false,
    isAutoRefresh: true,
    amount: 17535,
    currencyCode: "KZT",
    paymentMode: "Vac",
    purposeId1: "5dac333a-b947-4f47-a1eb-4140bc27ff7c",
    purposeId2: "42bad10d-916e-401f-9197-68d757a4ee8a",
    roleName: "Individual",
    notificationType: "none"
  },
  {
    // Student Application 
    isFamily: false,
    familySize: 1,
    fullName: "АЙСУЛУ КЕНЖЕБАЕВА",
    dateOfBirth: "2002-06-14",
    gender: "Female",
    passportNumber: "N13579024",
    passportExpiry: "2027-06-14",
    nationality: "KAZAKHSTAN",
    phoneNumber: "+77051234567",
    email: "<EMAIL>",
    visaCategory: "STUDY",
    visaSubcategory: "INDIVIDUAL",
    preferredCenters: ["ASTANA", "ALMATY"],
    dateFlexibility: "date_range",
    preferredDateRange: {
      from: "2024-08-20",
      to: "2024-09-10"
    },
    comments: "Поездка для поступления в университет Болоньи",
    // VFS System Codes
    countryCode: "kaz",
    missionCode: "ita",
    nationalityCode: "KAZ",
    languageCode: "ru-RU",
    dialCode: "7",
    vfsCenterCode: "AST",
    vfsVisaCategoryCode: "STU",
    vfsVisaSubcategoryCode: "VIS",
    applicantType: 0,
    applicantGroupId: 0,
    isEdit: false,
    isWaitlist: false,
    isAutoRefresh: true,
    amount: 17535,
    currencyCode: "KZT",
    paymentMode: "Vac",
    purposeId1: "5dac333a-b947-4f47-a1eb-4140bc27ff7c",
    purposeId2: "42bad10d-916e-401f-9197-68d757a4ee8a",
    roleName: "Individual",
    notificationType: "none"
  }
];

async function createVFSTestApplications() {
  console.log('Creating 5 VFS-compliant test applications for Kazakhstan users...');

  try {
    // Clear existing test applications
    await prisma.visaApplication.deleteMany({});
    console.log('✅ Cleared existing applications\n');

    for (let i = 0; i < kazakhstanVFSTestData.length; i++) {
      const applicationData = kazakhstanVFSTestData[i];
      
      console.log(`Creating application ${i + 1}: ${applicationData.fullName}`);
      console.log(`  Type: ${applicationData.isFamily ? 'Family' : 'Individual'} (${applicationData.familySize} ${applicationData.familySize === 1 ? 'person' : 'people'})`);
      console.log(`  Category: ${applicationData.visaCategory} - ${applicationData.visaSubcategory}`);
      console.log(`  VFS Codes: ${applicationData.vfsVisaCategoryCode}/${applicationData.vfsVisaSubcategoryCode} @ ${applicationData.vfsCenterCode}`);
      console.log(`  Amount: ${applicationData.amount} ${applicationData.currencyCode}`);
      
      const application = await prisma.visaApplication.create({
        data: {
          ...applicationData,
          preferredCenters: JSON.stringify(applicationData.preferredCenters),
          preferredDateRange: applicationData.preferredDateRange ? JSON.stringify(applicationData.preferredDateRange) : null,
          applicants: applicationData.applicants ? JSON.stringify(applicationData.applicants) : null,
        }
      });
      
      console.log(`  ✅ Created application ID: ${application.id}\n`);
    }
    
    console.log('🎉 Successfully created all 5 VFS-compliant test applications!');
    
    // Display summary
    const applications = await prisma.visaApplication.findMany({
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        fullName: true,
        visaCategory: true,
        vfsVisaCategoryCode: true,
        vfsCenterCode: true,
        isFamily: true,
        familySize: true,
        amount: true,
        currencyCode: true
      }
    });
    
    console.log('\n📋 Summary of created VFS applications:');
    console.log('ID'.padEnd(30) + 'Name'.padEnd(25) + 'Category'.padEnd(15) + 'VFS Code'.padEnd(10) + 'Center'.padEnd(8) + 'Type'.padEnd(12) + 'Amount');
    console.log('─'.repeat(110));
    
    applications.forEach((app) => {
      const type = app.isFamily ? `Family (${app.familySize})` : 'Individual';
      console.log(
        app.id.padEnd(30) +
        app.fullName.padEnd(25) +
        app.visaCategory.padEnd(15) +
        (app.vfsVisaCategoryCode || '').padEnd(10) +
        (app.vfsCenterCode || '').padEnd(8) +
        type.padEnd(12) +
        `${app.amount} ${app.currencyCode}`
      );
    });
    
    const totalAmount = applications.reduce((sum, app) => sum + app.amount, 0);
    console.log('\n💰 Total test applications amount: ' + totalAmount + ' KZT');
    
  } catch (error) {
    console.error('Error creating VFS test applications:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
createVFSTestApplications();