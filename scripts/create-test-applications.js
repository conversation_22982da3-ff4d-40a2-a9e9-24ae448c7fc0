const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Kazakhstan test data
const kazakhstanTestData = [
  {
    // Individual application
    isFamily: false,
    familySize: 1,
    fullName: "АЙДАР НАЗАРБАЕВ",
    dateOfBirth: "1985-03-15",
    gender: "Male",
    passportNumber: "N12345678",
    passportExpiry: "2027-03-15",
    nationality: "KAZAKHSTAN",
    phoneNumber: "+77012345678",
    email: "aidar.nazar<PERSON><PERSON>@gmail.com",
    visaCategory: "TOURISM",
    visaSubcategory: "INDIVIDUAL",
    preferredCenters: ["ALMATY"],
    dateFlexibility: "any_date",
    comments: "Первая поездка в Италию для туризма"
  },
  {
    // Family application with 2 people
    isFamily: true,
    familySize: 2,
    fullName: "ГУЛЬНАРА ТОКАЕВА",
    dateOfBirth: "1990-07-22",
    gender: "Female",
    passportNumber: "N87654321",
    passportExpiry: "2028-07-22",
    nationality: "KAZAKHSTAN",
    phoneNumber: "+77025555555",
    email: "<EMAIL>",
    visaCategory: "FAMILY",
    visaSubcategory: "FAMILY_VISIT",
    preferredCenters: ["ASTANA", "ALMATY"],
    dateFlexibility: "date_range",
    preferredDateRange: {
      from: "2024-09-01",
      to: "2024-09-30"
    },
    applicants: [
      {
        firstName: "ГУЛЬНАРА",
        lastName: "ТОКАЕВА",
        dateOfBirth: "1990-07-22",
        gender: "Female",
        passportNumber: "N87654321",
        passportExpiry: "2028-07-22",
        nationality: "KAZAKHSTAN",
        isEndorsedChild: false,
        relationshipToMain: "main"
      },
      {
        firstName: "АРМАН",
        lastName: "ТОКАЕВ",
        dateOfBirth: "1988-05-10",
        gender: "Male",
        passportNumber: "N11223344",
        passportExpiry: "2029-05-10",
        nationality: "KAZAKHSTAN",
        isEndorsedChild: false,
        relationshipToMain: "spouse"
      }
    ],
    comments: "Семейная поездка к родственникам в Милане"
  },
  {
    // Family application with children
    isFamily: true,
    familySize: 4,
    fullName: "СЕРИК АБАЕВ",
    dateOfBirth: "1982-11-05",
    gender: "Male",
    passportNumber: "N55667788",
    passportExpiry: "2026-11-05",
    nationality: "KAZAKHSTAN",
    phoneNumber: "+77077777777",
    email: "<EMAIL>",
    visaCategory: "TOURISM",
    visaSubcategory: "GROUP",
    preferredCenters: ["SHYMKENT", "ALMATY"],
    dateFlexibility: "any_date",
    applicants: [
      {
        firstName: "СЕРИК",
        lastName: "АБАЕВ",
        dateOfBirth: "1982-11-05",
        gender: "Male",
        passportNumber: "N55667788",
        passportExpiry: "2026-11-05",
        nationality: "KAZAKHSTAN",
        isEndorsedChild: false,
        relationshipToMain: "main"
      },
      {
        firstName: "МАДИНА",
        lastName: "АБАЕВА",
        dateOfBirth: "1985-08-18",
        gender: "Female",
        passportNumber: "N99887766",
        passportExpiry: "2027-08-18",
        nationality: "KAZAKHSTAN",
        isEndorsedChild: false,
        relationshipToMain: "spouse"
      },
      {
        firstName: "АЛИЯ",
        lastName: "АБАЕВА",
        dateOfBirth: "2015-04-12",
        gender: "Female",
        passportNumber: "N44556677",
        passportExpiry: "2025-04-12",
        nationality: "KAZAKHSTAN",
        isEndorsedChild: true,
        relationshipToMain: "child",
        parentPassportNumber: "N55667788",
        parentPassportExpiry: "2026-11-05"
      },
      {
        firstName: "ДАНИЯР",
        lastName: "АБАЕВ",
        dateOfBirth: "2018-09-30",
        gender: "Male",
        passportNumber: "N33445566",
        passportExpiry: "2028-09-30",
        nationality: "KAZAKHSTAN",
        isEndorsedChild: true,
        relationshipToMain: "child",
        parentPassportNumber: "N55667788",
        parentPassportExpiry: "2026-11-05"
      }
    ],
    comments: "Семейный отпуск в Риме и Флоренции с детьми"
  },
  {
    // Business application
    isFamily: false,
    familySize: 1,
    fullName: "ЖАНАТ МУСТАФИН",
    dateOfBirth: "1975-12-08",
    gender: "Male",
    passportNumber: "N98765432",
    passportExpiry: "2025-12-08",
    nationality: "KAZAKHSTAN",
    phoneNumber: "+***********",
    email: "<EMAIL>",
    visaCategory: "BUSINESS",
    visaSubcategory: "BUSINESS_MEETING",  
    preferredCenters: ["ALMATY"],
    dateFlexibility: "date_range",
    preferredDateRange: {
      from: "2024-10-15",
      to: "2024-10-25"
    },
    comments: "Деловая поездка для участия в международной конференции"
  },
  {
    // Student application
    isFamily: false,
    familySize: 1,
    fullName: "АЙСУЛУ КЕНЖЕБАЕВА",
    dateOfBirth: "2002-06-14",
    gender: "Female",
    passportNumber: "N13579024",
    passportExpiry: "2027-06-14",
    nationality: "KAZAKHSTAN",
    phoneNumber: "+***********",
    email: "<EMAIL>",
    visaCategory: "STUDY",
    visaSubcategory: "INDIVIDUAL",
    preferredCenters: ["ASTANA", "ALMATY"],
    dateFlexibility: "date_range",
    preferredDateRange: {
      from: "2024-08-20",
      to: "2024-09-10"
    },
    comments: "Поездка для поступления в университет Болоньи"
  }
];

async function createTestApplications() {
  console.log('Creating 5 test applications for Kazakhstan users...');

  try {
    for (let i = 0; i < kazakhstanTestData.length; i++) {
      const applicationData = kazakhstanTestData[i];
      
      console.log(`Creating application ${i + 1}: ${applicationData.fullName}`);
      
      const application = await prisma.visaApplication.create({
        data: {
          ...applicationData,
          preferredCenters: JSON.stringify(applicationData.preferredCenters),
          preferredDateRange: applicationData.preferredDateRange ? JSON.stringify(applicationData.preferredDateRange) : null,
          applicants: applicationData.applicants ? JSON.stringify(applicationData.applicants) : null,
        }
      });
      
      console.log(`✅ Created application ${application.id} for ${applicationData.fullName}`);
    }
    
    console.log('\n🎉 Successfully created all 5 test applications!');
    
    // Display summary
    const applications = await prisma.visaApplication.findMany({
      orderBy: { createdAt: 'desc' },
      take: 5
    });
    
    console.log('\n📋 Summary of created applications:');
    applications.forEach((app, index) => {
      console.log(`${index + 1}. ${app.fullName} - ${app.visaCategory} - ${app.isFamily ? 'Family' : 'Individual'} (${app.familySize} ${app.familySize === 1 ? 'person' : 'people'})`);
    });
    
  } catch (error) {
    console.error('Error creating test applications:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
createTestApplications();