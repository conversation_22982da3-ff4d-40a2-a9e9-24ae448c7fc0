# Dependencies
node_modules
npm-debug.log*

# Testing
coverage

# Next.js
.next/
out/

# Production build
build

# Environment variables
.env*.local

# Vercel
.vercel

# TypeScript
*.tsbuildinfo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Desktop.ini
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed

# Coverage directory used by tools like istanbul
lib-cov

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Claude
.claude/