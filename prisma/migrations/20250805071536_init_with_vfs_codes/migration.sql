-- CreateEnum
CREATE TYPE "public"."ApplicationStatus" AS ENUM ('Новая', 'Оплаченная', 'Завершенная');

-- CreateTable
CREATE TABLE "public"."visa_applications" (
    "id" TEXT NOT NULL,
    "isFamily" BOOLEAN NOT NULL DEFAULT false,
    "familySize" INTEGER NOT NULL DEFAULT 1,
    "applicants" JSONB,
    "fullName" TEXT NOT NULL,
    "dateOfBirth" TEXT NOT NULL,
    "gender" TEXT NOT NULL,
    "passportNumber" TEXT NOT NULL,
    "passportExpiry" TEXT NOT NULL,
    "nationality" TEXT NOT NULL DEFAULT 'KAZAKHSTAN',
    "phoneNumber" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "visaCategory" TEXT NOT NULL,
    "visaSubcategory" TEXT NOT NULL,
    "preferredCenters" JSONB NOT NULL,
    "dateFlexibility" TEXT NOT NULL,
    "preferredDateRange" JSONB,
    "countryCode" TEXT NOT NULL DEFAULT 'kaz',
    "missionCode" TEXT NOT NULL DEFAULT 'ita',
    "nationalityCode" TEXT NOT NULL DEFAULT 'KAZ',
    "languageCode" TEXT NOT NULL DEFAULT 'ru-RU',
    "dialCode" TEXT NOT NULL DEFAULT '7',
    "vfsCenterCode" TEXT,
    "vfsVisaCategoryCode" TEXT,
    "vfsVisaSubcategoryCode" TEXT,
    "applicantType" INTEGER NOT NULL DEFAULT 0,
    "applicantGroupId" INTEGER NOT NULL DEFAULT 0,
    "isEdit" BOOLEAN NOT NULL DEFAULT false,
    "isWaitlist" BOOLEAN NOT NULL DEFAULT false,
    "isAutoRefresh" BOOLEAN NOT NULL DEFAULT true,
    "amount" INTEGER NOT NULL DEFAULT 17535,
    "currencyCode" TEXT NOT NULL DEFAULT 'KZT',
    "paymentMode" TEXT NOT NULL DEFAULT 'Vac',
    "purposeId1" TEXT NOT NULL DEFAULT '5dac333a-b947-4f47-a1eb-4140bc27ff7c',
    "purposeId2" TEXT NOT NULL DEFAULT '42bad10d-916e-401f-9197-68d757a4ee8a',
    "roleName" TEXT NOT NULL DEFAULT 'Individual',
    "notificationType" TEXT NOT NULL DEFAULT 'none',
    "comments" TEXT,
    "dataProcessing" TEXT NOT NULL,
    "termsAcceptance" TEXT NOT NULL,
    "status" "public"."ApplicationStatus" NOT NULL DEFAULT 'Новая',
    "error" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "visa_applications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."visa_center_credentials" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "description" TEXT,
    "priority" TEXT NOT NULL DEFAULT 'standard',
    "applicationCount" INTEGER NOT NULL DEFAULT 0,
    "maxApplications" INTEGER NOT NULL DEFAULT 12,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "visa_center_credentials_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."application_submissions" (
    "id" TEXT NOT NULL,
    "applicationId" TEXT NOT NULL,
    "credentialId" TEXT NOT NULL,
    "submittedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" TEXT NOT NULL DEFAULT 'SUBMITTED',
    "notes" TEXT,

    CONSTRAINT "application_submissions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "visa_center_credentials_email_key" ON "public"."visa_center_credentials"("email");

-- CreateIndex
CREATE UNIQUE INDEX "application_submissions_applicationId_key" ON "public"."application_submissions"("applicationId");

-- AddForeignKey
ALTER TABLE "public"."application_submissions" ADD CONSTRAINT "application_submissions_applicationId_fkey" FOREIGN KEY ("applicationId") REFERENCES "public"."visa_applications"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."application_submissions" ADD CONSTRAINT "application_submissions_credentialId_fkey" FOREIGN KEY ("credentialId") REFERENCES "public"."visa_center_credentials"("id") ON DELETE CASCADE ON UPDATE CASCADE;
