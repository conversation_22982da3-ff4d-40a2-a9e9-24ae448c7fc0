generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model VisaApplication {
  id String @id @default(cuid())
  
  // Family Application Info
  isFamily <PERSON>olean @default(false)
  familySize Int @default(1)
  applicants Json? // Array of applicant objects
  
  // Main Applicant Info (for backward compatibility and main contact)
  fullName String
  firstName String?
  lastName String?
  middleName String?
  dateOfBirth String
  gender String
  
  // Passport Info
  passportNumber String
  passportExpiry String
  nationality String @default("KAZAKHSTAN")
  
  // Contact Info
  phoneNumber String
  email String
  
  // Visa Preferences
  visaCategory String
  visaSubcategory String
  preferredCenters Json
  dateFlexibility String
  preferredDateRange Json?
  
  // VFS System Codes
  countryCode String @default("kaz")
  missionCode String @default("ita")
  nationalityCode String @default("KAZ")
  languageCode String @default("ru-RU")
  dialCode String @default("7")
  
  // VFS Center Code (mapped from preferredCenters)
  vfsCenterCode String? // AST, ALA, SHY, UST, ATY
  
  // VFS Visa Category Codes
  vfsVisaCategoryCode String? // TOU, BUS, PV, OSS, etc.
  vfsVisaSubcategoryCode String? // Detailed subcategory codes
  
  // VFS Application Settings
  applicantType Int @default(0)
  applicantGroupId Int @default(0)
  isEdit Boolean @default(false)
  isWaitlist Boolean @default(false)
  isAutoRefresh Boolean @default(true)
  
  // VFS Payment Info
  amount Int @default(17535) // Base fee in KZT
  currencyCode String @default("KZT")
  paymentMode String @default("Vac")
  
  // VFS Privacy Consent
  purposeId1 String @default("************************************")
  purposeId2 String @default("42bad10d-916e-401f-9197-68d757a4ee8a")
  
  // VFS Role and Notification
  roleName String @default("Individual")
  notificationType String @default("none")
  
  // Additional Options
  comments String?
  
  // System fields
  status ApplicationStatus @default(NEW)
  error String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  submission ApplicationSubmission?
  
  @@map("visa_applications")
}

enum ApplicationStatus {
  NEW @map("Новая")
  PAID @map("Оплаченная") 
  COMPLETED @map("Завершенная")
}

model VisaCenterCredential {
  id String @id @default(cuid())
  email String @unique
  password String
  isActive Boolean @default(true)
  description String? // Optional description like "VFS Global Main Account"
  priority String @default("standard") // standard, prime, premium
  applicationCount Int @default(0) // Current number of applications
  maxApplications Int @default(12) // Maximum applications per account
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  applicationSubmissions ApplicationSubmission[]
  
  @@map("visa_center_credentials")
}

model ApplicationSubmission {
  id String @id @default(cuid())
  
  // Foreign keys
  applicationId String
  credentialId String
  
  // Relations
  application VisaApplication @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  credential VisaCenterCredential @relation(fields: [credentialId], references: [id], onDelete: Cascade)
  
  // Metadata
  submittedAt DateTime @default(now())
  status String @default("SUBMITTED") // SUBMITTED, CANCELLED, COMPLETED
  notes String? // Optional notes about submission
  
  @@unique([applicationId]) // One submission per application
  @@map("application_submissions")
}