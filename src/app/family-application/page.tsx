'use client'

import FamilyApplicationForm from '@/components/FamilyApplicationForm';
import { useRouter } from 'next/navigation';

export default function FamilyApplicationPage() {
  const router = useRouter();
  
  const handleToggleMode = () => {
    router.push('/');
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <FamilyApplicationForm onToggleMode={handleToggleMode} />
    </div>
  );
}