import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { status, error } = body;

    const result = await prisma.visaApplication.update({
      where: { id },
      data: { 
        status: status,
        error: error || null,
        updatedAt: new Date()
      }
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error updating application:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    // Get the application data for submission
    const application = await prisma.visaApplication.findUnique({
      where: { id }
    });

    if (!application) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }
    
    // Return the JSON of the application for submission
    return NextResponse.json({
      message: 'Application data retrieved for submission',
      applicationData: application
    });
  } catch (error) {
    console.error('Error retrieving application for submission:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}