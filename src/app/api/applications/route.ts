import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { visaApplicationSchema } from '@/lib/validation';
import { wappiService } from '@/lib/wappi';
import { mapVisaCategoryToVFSCode, mapVisaSubcategoryToVFSCode, mapCenterToVFSCode } from '@/lib/botPayloadConverter';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the data
    const validationResult = visaApplicationSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid data', details: validationResult.error.issues },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Map VFS codes
    const vfsVisaCategoryCode = mapVisaCategoryToVFSCode(data.visaCategory);
    const vfsVisaSubcategoryCode = mapVisaSubcategoryToVFSCode(data.visaSubcategory);
    const vfsCenterCode = mapCenterToVFSCode(data.preferredCenters);

    // For family applications, generate fullName from main applicant
    let fullName = data.fullName;
    let firstName = data.firstName;
    let lastName = data.lastName;
    let middleName = data.middleName;
    let dateOfBirth = data.dateOfBirth;
    let gender = data.gender;
    let passportNumber = data.passportNumber;
    let passportExpiry = data.passportExpiry;

    if (data.isFamily && data.applicants && data.applicants.length > 0) {
      // Find the main applicant (first one or the one marked as main)
      const mainApplicant = data.applicants.find(app => app.relationshipToMain === 'main') || data.applicants[0];
      
      if (mainApplicant) {
        fullName = mainApplicant.middleName 
          ? `${mainApplicant.firstName} ${mainApplicant.middleName} ${mainApplicant.lastName}`.trim()
          : `${mainApplicant.firstName} ${mainApplicant.lastName}`.trim();
        firstName = mainApplicant.firstName;
        lastName = mainApplicant.lastName;
        middleName = mainApplicant.middleName || null;
        dateOfBirth = mainApplicant.dateOfBirth;
        gender = mainApplicant.gender;
        passportNumber = mainApplicant.passportNumber;
        passportExpiry = mainApplicant.passportExpiry;
      }
    }

    // Insert into database using Prisma
    const result = await prisma.visaApplication.create({
      data: {
        // Family application info
        isFamily: data.isFamily || false,
        familySize: data.isFamily ? (data.applicants?.length || 1) : 1,
        applicants: data.isFamily ? data.applicants : undefined,
        
        // Main applicant info (either from individual form or main family member)
        fullName: fullName,
        firstName: firstName || null,
        lastName: lastName || null,
        middleName: middleName || null,
        dateOfBirth: dateOfBirth,
        gender: gender,
        passportNumber: passportNumber,
        passportExpiry: passportExpiry,
        nationality: 'KAZAKHSTAN',
        phoneNumber: data.phoneNumber,
        email: data.email,
        visaCategory: data.visaCategory,
        visaSubcategory: data.visaSubcategory,
        preferredCenters: data.preferredCenters,
        dateFlexibility: data.dateFlexibility,
        preferredDateRange: data.preferredDateRange || undefined,
        comments: data.comments || undefined,
        
        // VFS System Codes (auto-populated with defaults from schema)
        vfsCenterCode: vfsCenterCode,
        vfsVisaCategoryCode: vfsVisaCategoryCode,
        vfsVisaSubcategoryCode: vfsVisaSubcategoryCode,
      },
    });

    // Send WhatsApp notification to user
    try {
      await wappiService.sendApplicationNotification(result.id, data.phoneNumber);
    } catch (notificationError) {
      console.error('Error sending notification:', notificationError);
      // Don't fail the request if notification fails
    }

    return NextResponse.json(
      { message: 'Application submitted successfully', id: result.id },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error submitting application:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const applications = await prisma.visaApplication.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    return NextResponse.json(applications);
  } catch (error) {
    console.error('Error fetching applications:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}