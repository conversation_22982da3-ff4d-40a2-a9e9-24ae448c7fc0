import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { applicationId, credentialId, notes } = body;

    if (!applicationId || !credentialId) {
      return NextResponse.json(
        { error: 'Application ID and Credential ID are required' },
        { status: 400 }
      );
    }

    // Check if submission already exists
    const existingSubmission = await prisma.applicationSubmission.findUnique({
      where: { applicationId }
    });

    if (existingSubmission) {
      return NextResponse.json(
        { error: 'Application already has a submission record' },
        { status: 400 }
      );
    }

    const submission = await prisma.applicationSubmission.create({
      data: {
        applicationId,
        credentialId,
        notes: notes || null,
        status: 'SUBMITTED'
      },
      include: {
        application: true,
        credential: true
      }
    });

    return NextResponse.json(submission, { status: 201 });
  } catch (error) {
    console.error('Error creating submission:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const submissions = await prisma.applicationSubmission.findMany({
      include: {
        application: true,
        credential: true
      },
      orderBy: {
        submittedAt: 'desc'
      }
    });
    
    return NextResponse.json(submissions);
  } catch (error) {
    console.error('Error fetching submissions:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}