import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    // Get all active credentials ordered by priority and application count
    const credentials = await prisma.visaCenterCredential.findMany({
      where: { isActive: true },
      orderBy: [
        { priority: 'asc' }, // standard (0), prime (1), premium (2) 
        { applicationCount: 'asc' }, // least used first
      ]
    });

    if (credentials.length === 0) {
      return NextResponse.json(
        { error: 'No active credentials available' },
        { status: 404 }
      );
    }

    // Define priority order
    const priorityOrder = ['standard', 'prime', 'premium'];
    
    // Find the best available credential
    let bestCredential = null;
    
    for (const priority of priorityOrder) {
      const availableCredentials = credentials.filter(
        cred => cred.priority === priority && cred.applicationCount < cred.maxApplications
      );
      
      if (availableCredentials.length > 0) {
        // Get the one with least applications
        bestCredential = availableCredentials[0];
        break;
      }
    }

    if (!bestCredential) {
      // All credentials are at max capacity, return the standard one with least applications
      const standardCredentials = credentials.filter(cred => cred.priority === 'standard');
      if (standardCredentials.length > 0) {
        bestCredential = standardCredentials[0];
      } else {
        bestCredential = credentials[0];
      }
    }

    return NextResponse.json({
      credential: bestCredential,
      availableSlots: Math.max(0, bestCredential.maxApplications - bestCredential.applicationCount),
      allCredentials: credentials.map(cred => ({
        id: cred.id,
        email: cred.email,
        priority: cred.priority,
        applicationCount: cred.applicationCount,
        maxApplications: cred.maxApplications,
        availableSlots: Math.max(0, cred.maxApplications - cred.applicationCount)
      }))
    });
  } catch (error) {
    console.error('Error finding best credential:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}