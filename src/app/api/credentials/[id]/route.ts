import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { email, password, description, isActive, priority, maxApplications, applicationCount } = body;

    const credential = await prisma.visaCenterCredential.update({
      where: { id },
      data: {
        ...(email && { email }),
        ...(password && { password }),
        ...(description !== undefined && { description }),
        ...(isActive !== undefined && { isActive }),
        ...(priority && { priority }),
        ...(maxApplications !== undefined && { maxApplications }),
        ...(applicationCount !== undefined && { applicationCount }),
        updatedAt: new Date()
      }
    });

    return NextResponse.json(credential);
  } catch (error: any) {
    console.error('Error updating credential:', error);
    
    if (error.code === 'P2002') {
      return NextResponse.json(
        { error: 'Email already exists' },
        { status: 400 }
      );
    }
    
    if (error.code === 'P2025') {
      return NextResponse.json(
        { error: 'Credential not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    await prisma.visaCenterCredential.delete({
      where: { id }
    });

    return NextResponse.json({ message: 'Credential deleted successfully' });
  } catch (error: any) {
    console.error('Error deleting credential:', error);
    
    if (error.code === 'P2025') {
      return NextResponse.json(
        { error: 'Credential not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}