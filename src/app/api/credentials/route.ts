import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    const credentials = await prisma.visaCenterCredential.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    return NextResponse.json(credentials);
  } catch (error) {
    console.error('Error fetching credentials:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password, description, isActive, priority, maxApplications } = body;

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    const credential = await prisma.visaCenterCredential.create({
      data: {
        email,
        password,
        description: description || null,
        isActive: isActive !== undefined ? isActive : true,
        priority: priority || 'standard',
        maxApplications: maxApplications || 12,
        applicationCount: 0,
      },
    });

    return NextResponse.json(credential, { status: 201 });
  } catch (error: any) {
    console.error('Error creating credential:', error);
    
    if (error.code === 'P2002') {
      return NextResponse.json(
        { error: 'Email already exists' },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}