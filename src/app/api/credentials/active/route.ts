import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    const activeCredential = await prisma.visaCenterCredential.findFirst({
      where: { isActive: true },
      orderBy: { updatedAt: 'desc' }
    });
    
    if (!activeCredential) {
      return NextResponse.json(
        { error: 'No active credentials found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(activeCredential);
  } catch (error) {
    console.error('Error fetching active credential:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}