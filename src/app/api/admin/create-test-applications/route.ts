import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// 10 фиксированных профилей для Алматы
const TEST_PROFILES = [
  {
    firstName: "AIDAR", lastName: "NAZARBAYEV", middleName: "SULTANOVICH",
    dateOfBirth: "1985-03-15", gender: "Male" as const,
    passportNumber: "*********", passportExpiry: "2029-03-15",
    phoneNumber: "+77771234567", email: "<EMAIL>"
  },
  {
    firstName: "AIDA", lastName: "KAZAKHBAYEVA", middleName: "ASKAROVNA", 
    dateOfBirth: "1990-07-22", gender: "Female" as const,
    passportNumber: "*********", passportExpiry: "2028-07-22",
    phoneNumber: "+77012345678", email: "<EMAIL>"
  },
  {
    firstName: "ARMAN", lastName: "TOLEUBAYEV", middleName: "",
    dateOfBirth: "1988-11-10", gender: "Male" as const, 
    passportNumber: "*********", passportExpiry: "2030-11-10",
    phoneNumber: "+77051112233", email: "<EMAIL>"
  },
  {
    firstName: "DANA", lastName: "SERIKOVA", middleName: "BOLATKYZY",
    dateOfBirth: "1992-05-08", gender: "Female" as const,
    passportNumber: "*********", passportExpiry: "2029-05-08", 
    phoneNumber: "+77761234567", email: "<EMAIL>"
  },
  {
    firstName: "BAUYRZHAN", lastName: "ALMATY", middleName: "KANATOVICH",
    dateOfBirth: "1987-09-18", gender: "Male" as const,
    passportNumber: "*********", passportExpiry: "2028-09-18",
    phoneNumber: "+77021234567", email: "<EMAIL>"
  },
  {
    firstName: "GULNARA", lastName: "ZHAKUPOVA", middleName: "ERLANKYZY",
    dateOfBirth: "1991-12-03", gender: "Female" as const,
    passportNumber: "*********", passportExpiry: "2030-12-03",
    phoneNumber: "+77751234567", email: "<EMAIL>"
  },
  {
    firstName: "NURLAN", lastName: "KAZAKHOV", middleName: "ALIKHANOVICH", 
    dateOfBirth: "1986-04-25", gender: "Male" as const,
    passportNumber: "*********", passportExpiry: "2029-04-25",
    phoneNumber: "+77781234567", email: "<EMAIL>"
  },
  {
    firstName: "SALTANAT", lastName: "AIDAROVA", middleName: "NURLANKYZY",
    dateOfBirth: "1993-08-14", gender: "Female" as const,
    passportNumber: "*********", passportExpiry: "2028-08-14",
    phoneNumber: "+77031234567", email: "<EMAIL>"
  },
  {
    firstName: "AZAT", lastName: "KAZAKHBEK", middleName: "",
    dateOfBirth: "1989-01-20", gender: "Male" as const,
    passportNumber: "*********", passportExpiry: "2030-01-20",
    phoneNumber: "+***********", email: "<EMAIL>"
  },
  {
    firstName: "ZHANNA", lastName: "ALMATYKZ", middleName: "SERIKKYZY",
    dateOfBirth: "1994-06-30", gender: "Female" as const,
    passportNumber: "*********", passportExpiry: "2029-06-30",
    phoneNumber: "+***********", email: "<EMAIL>"
  }
];

// Варианты визовых предпочтений
const VISA_VARIANTS = [
  {
    visaCategory: "TOURISM",
    visaSubcategory: "INDIVIDUAL"
  },
  {
    visaCategory: "BUSINESS", 
    visaSubcategory: "CONFERENCE"
  },
  {
    visaCategory: "TOURISM",
    visaSubcategory: "GROUP"
  }
];

function generateRealisticCreatedAt(nextMonth: Date, index: number) {
  // Распределить записи по месяцу равномерно
  const startDate = new Date(nextMonth.getFullYear(), nextMonth.getMonth(), 1);
  const endDate = new Date(nextMonth.getFullYear(), nextMonth.getMonth() + 1, 0);
  const totalDays = endDate.getDate();
  
  const dayOffset = Math.floor((totalDays / 10) * index) + 1;
  const randomHour = Math.floor(Math.random() * 12) + 9; // 9:00 - 21:00
  const randomMinute = Math.floor(Math.random() * 60);
  
  return new Date(
    startDate.getFullYear(), 
    startDate.getMonth(), 
    Math.min(dayOffset, totalDays),
    randomHour, 
    randomMinute
  );
}

export async function POST() {
  try {
    // Проверить права администратора
    const session = await getServerSession(authOptions);
    if (!session?.user || (session.user as any).role !== 'admin') {
      return NextResponse.json({ error: 'Доступ запрещен' }, { status: 401 });
    }

    // Получить следующий месяц
    const today = new Date();
    const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);
    const nextMonthEnd = new Date(today.getFullYear(), today.getMonth() + 2, 0);
    
    // Проверить существующие тестовые записи на следующий месяц
    const existingTestApps = await prisma.visaApplication.count({
      where: {
        AND: [
          {
            email: {
              contains: 'test.booking.'
            }
          },
          {
            createdAt: {
              gte: nextMonth,
              lte: nextMonthEnd
            }
          }
        ]
      }
    });

    if (existingTestApps >= 10) {
      return NextResponse.json({ 
        message: `Тестовые записи на ${nextMonth.toLocaleDateString('ru-RU', { month: 'long', year: 'numeric' })} уже созданы`,
        existing: existingTestApps,
        month: nextMonth.toLocaleDateString('ru-RU', { month: 'long', year: 'numeric' })
      });
    }

    // Создать тестовые записи
    const toCreate = 10 - existingTestApps;
    const createdApplications = [];

    for (let i = 0; i < toCreate; i++) {
      const profile = TEST_PROFILES[i % TEST_PROFILES.length];
      const visaVariant = VISA_VARIANTS[i % VISA_VARIANTS.length];
      const createdAt = generateRealisticCreatedAt(nextMonth, i);
      
      // Генерировать fullName
      const fullName = profile.middleName 
        ? `${profile.firstName} ${profile.middleName} ${profile.lastName}`
        : `${profile.firstName} ${profile.lastName}`;

      const application = await prisma.visaApplication.create({
        data: {
          // Основная информация
          isFamily: false,
          familySize: 1,
          applicants: null,
          
          // Данные заявителя
          fullName: fullName,
          firstName: profile.firstName,
          lastName: profile.lastName, 
          middleName: profile.middleName || null,
          dateOfBirth: profile.dateOfBirth,
          gender: profile.gender,
          passportNumber: profile.passportNumber,
          passportExpiry: profile.passportExpiry,
          nationality: 'KAZAKHSTAN',
          
          // Контакты
          phoneNumber: profile.phoneNumber,
          email: profile.email,
          
          // Визовые предпочтения
          visaCategory: visaVariant.visaCategory,
          visaSubcategory: visaVariant.visaSubcategory,
          preferredCenters: ["ALMATY"],
          dateFlexibility: "any_date",
          preferredDateRange: null,
          
          // VFS коды (будут заполнены автоматически из схемы)
          vfsCenterCode: "ALA",
          vfsVisaCategoryCode: visaVariant.visaCategory === "TOURISM" ? "TOU" : "BUS",
          vfsVisaSubcategoryCode: "VIS",
          
          // Системные поля
          status: "NEW",
          createdAt: createdAt,
          updatedAt: createdAt,
          
          // Комментарий для идентификации
          comments: `Системная тестовая запись для бронирования. Создана: ${new Date().toLocaleDateString('ru-RU')}`
        }
      });
      
      createdApplications.push(application);
    }

    return NextResponse.json({
      message: `Успешно создано ${createdApplications.length} тестовых заявок на ${nextMonth.toLocaleDateString('ru-RU', { month: 'long', year: 'numeric' })}`,
      created: createdApplications.length,
      total: existingTestApps + createdApplications.length,
      month: nextMonth.toLocaleDateString('ru-RU', { month: 'long', year: 'numeric' }),
      applications: createdApplications.map(app => ({
        id: app.id,
        fullName: app.fullName,
        email: app.email,
        createdAt: app.createdAt
      }))
    });

  } catch (error) {
    console.error('Ошибка при создании тестовых заявок:', error);
    return NextResponse.json(
      { error: 'Внутренняя ошибка сервера' },
      { status: 500 }
    );
  }
}

// GET endpoint для проверки статуса
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || (session.user as any).role !== 'admin') {
      return NextResponse.json({ error: 'Доступ запрещен' }, { status: 401 });
    }

    const today = new Date();
    const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);
    const nextMonthEnd = new Date(today.getFullYear(), today.getMonth() + 2, 0);
    
    const existingTestApps = await prisma.visaApplication.count({
      where: {
        AND: [
          {
            email: {
              contains: 'test.booking.'
            }
          },
          {
            createdAt: {
              gte: nextMonth,
              lte: nextMonthEnd
            }
          }
        ]
      }
    });

    return NextResponse.json({
      existing: existingTestApps,
      needed: Math.max(0, 10 - existingTestApps),
      month: nextMonth.toLocaleDateString('ru-RU', { month: 'long', year: 'numeric' }),
      canCreate: existingTestApps < 10
    });

  } catch (error) {
    console.error('Ошибка при проверке статуса:', error);
    return NextResponse.json(
      { error: 'Внутренняя ошибка сервера' },
      { status: 500 }
    );
  }
}