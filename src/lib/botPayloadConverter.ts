// Utility functions for converting application data to bot API format

export interface BotApplicationData {
  centerCode: string;
  visaCategoryCode: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  gender: 'M' | 'F';
  contactNumber: string;
  passportNumber: string;
  passportExpirtyDate: string;
  dateOfBirth: string;
  nationalityCode: 'KAZ';
  isEndorsedChild: boolean;
  parentPassportNumber?: string;
  parentPassportExpiry?: string;
}

export interface BotPayload {
  application_id: string;
  account_id?: string;
  email: string;
  password: string;
  description?: string;
  application_data: BotApplicationData;
}

// Helper function to format phone number (remove country code)
export const formatPhoneNumber = (phone: string): string => {
  // Remove +7 country code and any non-digits, keep only the number
  return phone.replace(/^\+?7/, '').replace(/\D/g, '');
};

// Helper function to map UI visa categories to VFS codes
export const mapVisaCategoryToVFSCode = (category: string): string => {
  const categoryMap: { [key: string]: string } = {
    'TOURISM': 'TOU',
    'BUSINESS': 'BUS', 
    'FAMILY': 'FAM',
    'STUDY': 'STU',
    'MEDICAL': 'MED',
    'TRANSIT': 'TRA',
    'OTHER': 'OSS'
  };
  return categoryMap[category] || 'TOU';
};

// Helper function to map UI visa subcategories to VFS codes
export const mapVisaSubcategoryToVFSCode = (subcategory: string): string => {
  const subcategoryMap: { [key: string]: string } = {
    'INDIVIDUAL': 'VIS',
    'GROUP': 'TOU',
    'FAMILY_VISIT': 'PV',
    'BUSINESS_MEETING': 'BUS',
    'CONFERENCE': 'BUS',
    'MEDICAL_TREATMENT': 'MED',
    'EDUCATION': 'STU',
    'WORK': 'WORK',
    'TRANSIT': 'TRA'
  };
  return subcategoryMap[subcategory] || 'VIS';
};

// Helper function to map preferred centers to VFS center codes
export const mapCenterToVFSCode = (centers: string[]): string => {
  const centerMap: { [key: string]: string } = {
    'Almaty': 'ALM',
    'ALMATY': 'ALM',
    'Astana': 'AST',
    'ASTANA': 'AST', 
    'Shymkent': 'SHY',
    'SHYMKENT': 'SHY',
    'Atyrau': 'ATY',
    'ATYRAU': 'ATY',
    'Ust_Kamenogorsk': 'OSK',
    'UST_KAMENOGORSK': 'OSK'
  };
  
  // Return first mapped center or default to AST
  for (const center of centers) {
    if (centerMap[center]) {
      return centerMap[center];
    }
  }
  return 'AST'; // Default to Astana
};

// Helper function to convert gender
export const convertGender = (gender: string): 'M' | 'F' => {
  return gender === 'Male' ? 'M' : 'F';
};

// Helper function to format date to YYYY-MM-DD
export const formatDate = (dateString: string): string => {
  // Handle various date formats and convert to YYYY-MM-DD
  const date = new Date(dateString);
  if (isNaN(date.getTime())) {
    throw new Error(`Invalid date format: ${dateString}`);
  }
  return date.toISOString().split('T')[0];
};

// Main conversion function to match bot's expected structure
export const convertApplicationToBotPayload = (
  applicationId: string,
  applicationData: any,
  credentials: { email: string; password: string },
  accountId?: string,
  description?: string
): BotPayload => {
  let firstName, lastName, middleName;
  
  // Debug logging
  console.log('=== BOT PAYLOAD CONVERTER DEBUG ===');
  console.log('Application Data:', applicationData);
  console.log('firstName field:', applicationData.firstName);
  console.log('lastName field:', applicationData.lastName);
  console.log('fullName field:', applicationData.fullName);
  console.log('isFamily:', applicationData.isFamily);
  
  // Handle name extraction for both family and individual applications
  if (applicationData.isFamily && applicationData.applicants && applicationData.applicants.length > 0) {
    // For family applications, use the main applicant (first one)
    const applicants = typeof applicationData.applicants === 'string' 
      ? JSON.parse(applicationData.applicants) 
      : applicationData.applicants;
    const mainApplicant = applicants[0];
    firstName = mainApplicant.firstName;
    lastName = mainApplicant.lastName;  
    middleName = mainApplicant.middleName;
  } else {
    // Individual application - try individual fields first, then fullName
    if (applicationData.firstName && applicationData.lastName) {
      // Use individual name fields if available
      firstName = applicationData.firstName;
      lastName = applicationData.lastName;
      middleName = applicationData.middleName || "";
    } else if (applicationData.fullName) {
      // Fallback to parsing fullName
      const nameParts = applicationData.fullName.trim().split(/\s+/);
      if (nameParts.length >= 2) {
        firstName = nameParts[0];
        lastName = nameParts.slice(-1)[0]; // Last part as lastName
        // Middle name is everything between first and last
        middleName = nameParts.slice(1, -1).join(' ') || "";
      } else {
        // Only one name provided
        firstName = nameParts[0];
        lastName = nameParts[0]; // Use same as firstName
        middleName = "";
      }
    } else {
      throw new Error('No name information available in application data');
    }
  }

  // Debug logging for extracted names
  console.log('Extracted names:', { firstName, lastName, middleName });

  // Get center code from preferred centers (handle JSON string from database)
  let preferredCenters = [];
  if (applicationData.preferredCenters) {
    if (typeof applicationData.preferredCenters === 'string') {
      try {
        preferredCenters = JSON.parse(applicationData.preferredCenters);
    } catch {
        preferredCenters = [applicationData.preferredCenters]; // Single center as string
      }
    } else if (Array.isArray(applicationData.preferredCenters)) {
      preferredCenters = applicationData.preferredCenters;
    }
  }
  const centerCode = mapCenterToVFSCode(preferredCenters);
  
  // Get visa category code
  const visaCategoryCode = mapVisaCategoryToVFSCode(applicationData.visaCategory);

  const application_data: BotApplicationData = {
    centerCode: centerCode,
    visaCategoryCode: visaCategoryCode,
    firstName: firstName,
    middleName: middleName || "",
    lastName: lastName,
    gender: convertGender(applicationData.gender),
    contactNumber: formatPhoneNumber(applicationData.phoneNumber),
    passportNumber: applicationData.passportNumber,
    passportExpirtyDate: formatDate(applicationData.passportExpiry),
    dateOfBirth: formatDate(applicationData.dateOfBirth),
    nationalityCode: 'KAZ',
    isEndorsedChild: false, // For main applicant always false
    parentPassportNumber: "",
    parentPassportExpiry: ""
  };

  return {
    application_id: applicationId,
    ...(accountId && { account_id: accountId }),
    email: credentials.email,
    password: credentials.password,
    ...(description && { description }),
    application_data
  };
};

// Validation function to ensure all required fields are present
export const validateBotPayload = (payload: BotPayload): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Required root fields
  if (!payload.application_id) errors.push('application_id is required');
  if (!payload.email) errors.push('email is required');
  if (!payload.password) errors.push('password is required');

  // Required application_data fields
  const data = payload.application_data;
  if (!data.centerCode) errors.push('centerCode is required');
  if (!data.visaCategoryCode) errors.push('visaCategoryCode is required');
  if (!data.firstName) errors.push('firstName is required');
  if (!data.lastName) errors.push('lastName is required');
  if (!data.gender || !['M', 'F'].includes(data.gender)) {
    errors.push('gender must be "M" or "F"');
  }
  if (!data.contactNumber) errors.push('contactNumber is required');
  if (!data.passportNumber) errors.push('passportNumber is required');
  if (!data.passportExpirtyDate) errors.push('passportExpirtyDate is required');
  if (!data.dateOfBirth) errors.push('dateOfBirth is required');
  if (data.nationalityCode !== 'KAZ') {
    errors.push('nationalityCode must be "KAZ"');
  }
  if (typeof data.isEndorsedChild !== 'boolean') {
    errors.push('isEndorsedChild must be boolean');
  }

  // Validate child-specific fields
  if (data.isEndorsedChild) {
    if (!data.parentPassportNumber) {
      errors.push('parentPassportNumber is required for children');
    }
    if (!data.parentPassportExpiry) {
      errors.push('parentPassportExpiry is required for children');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};