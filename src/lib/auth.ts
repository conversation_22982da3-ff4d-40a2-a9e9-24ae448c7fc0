import NextAuth, { NextAuthOptions } from "next-auth"
import Credentials<PERSON>rovider from "next-auth/providers/credentials"

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        identifier: { label: "Email or Phone", type: "text" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.identifier || !credentials?.password) {
          return null
        }

        const adminEmail = process.env.ADMIN_EMAIL
        const adminPhone = process.env.ADMIN_PHONE
        const adminPassword = process.env.ADMIN_PASSWORD

        if (!adminEmail || !adminPhone || !adminPassword) {
          console.error("Admin credentials not configured")
          return null
        }

        const isValidIdentifier = 
          credentials.identifier === adminEmail || 
          credentials.identifier === adminPhone

        if (isValidIdentifier && credentials.password === adminPassword) {
          return {
            id: "admin",
            email: adminEmail,
            name: "Ad<PERSON>",
            role: "admin"
          }
        }

        return null
      }
    })
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = (user as any).role
      }
      return token
    },
    async session({ session, token }) {
      if (token.role) {
        (session.user as any).role = token.role
      }
      return session
    }
  },
  pages: {
    signIn: "/auth/signin",
  },
  session: {
    strategy: "jwt",
  }
}

export default NextAuth(authOptions)