interface WappiResponse {
  status: string;
  timestamp: number;
  time: string;
  message_id: string;
  task_id: string;
  uuid?: string;
}

interface WappiMessage {
  body: string;
  recipient: string;
}

export class WappiService {
  private readonly baseUrl = 'https://wappi.pro/api/sync/message/send';
  private readonly authorization: string;
  private readonly profileId: string;

  constructor() {
    this.authorization = process.env.WAPPI_AUTHORIZATION || '';
    this.profileId = process.env.WAPPI_PROFILE_ID || '';
    
    if (!this.authorization || !this.profileId) {
      console.warn('Wappi credentials not configured. Notifications will not be sent.');
    }
  }

  async sendMessage(recipient: string, message: string): Promise<WappiResponse | null> {
    if (!this.authorization || !this.profileId) {
      console.log('Wappi not configured, skipping notification');
      return null;
    }

    const messageData: WappiMessage = {
      body: message,
      recipient: recipient
    };

    try {
      const response = await fetch(`${this.baseUrl}?profile_id=${this.profileId}`, {
        method: 'POST',
        headers: {
          'accept': 'application/json',
          'Authorization': this.authorization,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(messageData),
      });

      if (!response.ok) {
        throw new Error(`Wappi API error: ${response.status} ${response.statusText}`);
      }

      const result: WappiResponse = await response.json();
      console.log('Wappi notification sent successfully:', result);
      return result;
    } catch (error) {
      console.error('Error sending Wappi notification:', error);
      return null;
    }
  }

  async sendApplicationNotification(applicationId: string, phoneNumber: string): Promise<WappiResponse | null> {
    const message = `Ваша анкета на визу успешно заполнена!

ID анкеты: ${applicationId}

Ваша заявка принята и находится в обработке. Наш менеджер свяжется с вами в ближайшее время для уточнения деталей и дальнейших инструкций.

Спасибо за обращение!`;

    // Clean phone number (remove +, spaces, etc.)
    const cleanPhone = phoneNumber.replace(/[^\d]/g, '');
    
    return this.sendMessage(cleanPhone, message);
  }
}

export const wappiService = new WappiService();