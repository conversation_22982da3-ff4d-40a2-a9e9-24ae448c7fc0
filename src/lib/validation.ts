import { z } from 'zod';

// Individual applicant schema with flexible validation
export const applicantSchema = z.object({
  firstName: z.string(),
  lastName: z.string(), 
  middleName: z.string().optional().or(z.literal("")),
  dateOfBirth: z.string(),
  gender: z.enum(["Male", "Female"], { message: "Пол обязателен" }),
  passportNumber: z.string(),
  passportExpiry: z.string(),
  nationality: z.string().default("KAZAKHSTAN"),
  
  // Child-specific fields
  isEndorsedChild: z.boolean().default(false),
  parentPassportNumber: z.string().optional(),
  parentPassportExpiry: z.string().optional(),
  
  // Relationship to main applicant
  relationshipToMain: z.enum(["main", "spouse", "child", "other"]).default("main"),
}).refine((data) => {
  // Check if this applicant has been "started" (any key field filled)
  const hasStarted = data.firstName || data.lastName || data.dateOfBirth || data.passportNumber;
  
  if (!hasStarted) {
    // Empty applicant is OK (user just added them but hasn't started filling)
    return true;
  }
  
  // If started, validate all required fields
  const isValid = 
    data.firstName && data.firstName.length > 0 && /^[A-Z][A-Z\s]*$/.test(data.firstName) &&
    data.lastName && data.lastName.length > 0 && /^[A-Z][A-Z\s]*$/.test(data.lastName) &&
    data.dateOfBirth && data.dateOfBirth.length > 0 &&
    data.passportNumber && data.passportNumber.length > 0 && /^[A-Z][0-9]{8}$/.test(data.passportNumber) &&
    data.passportExpiry && data.passportExpiry.length > 0;
    
  // Additional validation for child
  if (data.isEndorsedChild && isValid) {
    return data.parentPassportNumber && data.parentPassportExpiry;
  }
  
  return isValid;
}, {
  message: "Заполните все обязательные поля для этого заявителя",
  path: ["firstName"]
});

export const visaApplicationSchema = z.object({
  // Family Application Info
  isFamily: z.boolean().default(false),
  applicants: z.array(applicantSchema).min(1, "Минимум один заявитель").max(5, "Максимум 5 заявителей").optional(),
  
  // Main Applicant Info (for backward compatibility and single applications)
  fullName: z.string()
    .min(1, "Полное имя обязательно")
    .regex(/^[A-Z][A-Z\s]+$/, "Только латинские буквы в верхнем регистре")
    .optional(),
    
  // Individual name fields (VFS preferred format)
  firstName: z.string().min(1, "Имя обязательно").regex(/^[A-Z][A-Z\s]*$/, "Только латинские буквы в верхнем регистре").optional(),
  lastName: z.string().min(1, "Фамилия обязательна").regex(/^[A-Z][A-Z\s]*$/, "Только латинские буквы в верхнем регистре").optional(),
  middleName: z.string().regex(/^[A-Z][A-Z\s]*$/, "Только латинские буквы в верхнем регистре").optional().or(z.literal("")),
  
  dateOfBirth: z.string()
    .min(1, "Дата рождения обязательна")
    .optional(),
  
  gender: z.enum(["Male", "Female"], {
    message: "Пол обязателен"
  }).optional(),
  
  // Passport Info
  passportNumber: z.string()
    .min(1, "Номер паспорта обязателен")
    .regex(/^[A-Z][0-9]{8}$/, "Формат: 1 буква + 8 цифр")
    .optional(),
  
  passportExpiry: z.string()
    .min(1, "Срок действия паспорта обязателен")
    .optional(),
  
  // Contact Info
  phoneNumber: z.string()
    .min(1, "Номер телефона обязателен")
    .regex(/^\+7[0-9]{10}$/, "Формат: +7XXXXXXXXXX"),
  
  email: z.string()
    .min(1, "Email обязателен")
    .email("Введите корректный email"),
  
  // Visa Preferences
  visaCategory: z.string()
    .min(1, "Категория визы обязательна"),
  
  visaSubcategory: z.string()
    .min(1, "Цель поездки обязательна"),
  
  preferredCenters: z.array(z.string())
    .min(1, "Выберите хотя бы один визовый центр"),
  
  dateFlexibility: z.enum(["any_date", "date_range"], {
    message: "Гибкость по датам обязательна"
  }),
  
  preferredDateRange: z.object({
    from: z.string().optional(),
    to: z.string().optional()
  }).optional(),
  
  // Additional Options
  comments: z.string().optional(),
  
}).refine((data) => {
  // For individual applications (not family), require main applicant fields
  if (!data.isFamily) {
    return data.fullName && data.dateOfBirth && data.gender && 
           data.passportNumber && data.passportExpiry;
  }
  
  // For family applications, ensure at least one complete applicant exists
  if (data.isFamily && data.applicants) {
    const completeApplicants = data.applicants.filter(applicant => {
      const hasStarted = applicant.firstName || applicant.lastName || 
                        applicant.dateOfBirth || applicant.passportNumber;
      return hasStarted;
    });
    
    return completeApplicants.length > 0;
  }
  
  return true;
}, {
  message: "Должен быть заполнен хотя бы один заявитель",
  path: ["applicants"]
});

export type ApplicantData = z.infer<typeof applicantSchema>;
export type VisaApplicationFormData = z.infer<typeof visaApplicationSchema>;