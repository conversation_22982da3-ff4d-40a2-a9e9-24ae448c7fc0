import { pgTable, text, timestamp, uuid, jsonb, pgEnum } from 'drizzle-orm/pg-core';

export const applicationStatusEnum = pgEnum('application_status', ['Новая', 'Оплаченная', 'Завершенная']);

export const visaApplications = pgTable('visa_applications', {
  id: uuid('id').defaultRandom().primaryKey(),
  
  // Personal Info
  fullName: text('full_name').notNull(),
  dateOfBirth: text('date_of_birth').notNull(),
  gender: text('gender').notNull(),
  
  // Passport Info
  passportNumber: text('passport_number').notNull(),
  passportExpiry: text('passport_expiry').notNull(),
  nationality: text('nationality').default('KAZAKHSTAN'),
  
  // Contact Info
  phoneNumber: text('phone_number').notNull(),
  email: text('email').notNull(),
  
  // Visa Preferences
  visaCategory: text('visa_category').notNull(),
  visaSubcategory: text('visa_subcategory').notNull(),
  preferredCenters: jsonb('preferred_centers').notNull(),
  dateFlexibility: text('date_flexibility').notNull(),
  preferredDateRange: jsonb('preferred_date_range'),
  
  // Additional Options
  urgency: text('urgency').notNull(),
  alternativeOptions: jsonb('alternative_options'),
  comments: text('comments'),
  
  
  // System fields
  status: applicationStatusEnum('status').default('Новая').notNull(),
  error: text('error'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});