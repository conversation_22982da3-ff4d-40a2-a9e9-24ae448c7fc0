import { useState } from 'react'
import FamilyApplicationForm from '../components/FamilyApplicationForm'

/**
 * Manual test page to validate the FamilyApplicationForm submit button logic
 * Visit /test-submit-button to test the functionality
 */
export default function TestSubmitButtonPage() {
  const [mode, setMode] = useState<'family' | 'individual'>('family')

  const toggleMode = () => {
    setMode(mode === 'family' ? 'individual' : 'family')
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Submit Button Logic Test
          </h1>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h2 className="text-lg font-semibold text-blue-900 mb-2">Test Instructions</h2>
            <ul className="list-disc list-inside text-blue-800 space-y-1">
              <li>Initially, the submit button should be <strong>disabled</strong> (form is invalid)</li>
              <li>Fill out all required fields correctly - button should become <strong>enabled</strong></li>
              <li>Clear a required field - button should become <strong>disabled</strong> again</li>
              <li>Submit the form - button should be <strong>disabled</strong> during submission</li>
              <li>After submission completes - button should be <strong>enabled</strong> again</li>
            </ul>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <h2 className="text-lg font-semibold text-green-900 mb-2">✅ Logic Implemented</h2>
            <code className="text-green-800 bg-green-100 px-2 py-1 rounded">
              disabled={'{isSubmitting || !isValid}'}
            </code>
            <p className="text-green-700 mt-2">
              The submit button now correctly depends only on form validation state and submission status,
              not on the number of family members.
            </p>
          </div>

          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <h2 className="text-lg font-semibold text-red-900 mb-2">❌ Previous Logic (Fixed)</h2>
            <code className="text-red-800 bg-red-100 px-2 py-1 rounded line-through">
              disabled={'{isSubmitting || fields.length === 0}'}
            </code>
            <p className="text-red-700 mt-2">
              The old logic incorrectly depended on the number of family members instead of form validation.
            </p>
          </div>
        </div>

        {mode === 'family' ? (
          <FamilyApplicationForm onToggleMode={toggleMode} />
        ) : (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <p>Individual form would be here. Click to toggle back to family form.</p>
            <button
              onClick={toggleMode}
              className="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Switch to Family Form
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
