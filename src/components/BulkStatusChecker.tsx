"use client"

import { useState } from 'react';

interface ApplicationStatus {
  application_id: string;
  status: string;
  message?: string;
  progress?: string;
  timestamp?: string;
}

interface BulkStatusCheckerProps {
  applicationIds: string[];
  onStatusUpdate?: (statuses: ApplicationStatus[]) => void;
}

export default function BulkStatusChecker({ applicationIds, onStatusUpdate }: BulkStatusCheckerProps) {
  const [statuses, setStatuses] = useState<{ [key: string]: ApplicationStatus }>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState({ current: 0, total: 0 });

  const checkAllStatuses = async () => {
    if (applicationIds.length === 0) return;
    
    setLoading(true);
    setError(null);
    setProgress({ current: 0, total: applicationIds.length });
    
    const newStatuses: { [key: string]: ApplicationStatus } = {};
    
    try {
      for (let i = 0; i < applicationIds.length; i++) {
        const appId = applicationIds[i];
        setProgress({ current: i + 1, total: applicationIds.length });
        
        try {
          const response = await fetch(`http://localhost:5000/api/application-status/${appId}`);
          
          if (response.ok) {
            const statusData = await response.json() as ApplicationStatus;
            newStatuses[appId] = statusData;
          } else {
            newStatuses[appId] = {
              application_id: appId,
              status: 'unknown',
              message: 'Статус не найден'
            };
          }
        } catch {
          newStatuses[appId] = {
            application_id: appId,
            status: 'error',
            message: 'Ошибка соединения'
          };
        }
        
        // Small delay to avoid overwhelming the bot API
        if (i < applicationIds.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }
      
      setStatuses(newStatuses);
      onStatusUpdate?.(Object.values(newStatuses));
      
    } catch (err) {
      console.error('Error checking bulk status:', err);
      setError('Ошибка при проверке статусов');
    } finally {
      setLoading(false);
      setProgress({ current: 0, total: 0 });
    }
  };

  const getStatusSummary = () => {
    const statusCounts = {
      completed: 0,
      processing: 0,
      failed: 0,
      pending: 0,
      unknown: 0
    };

    Object.values(statuses).forEach(status => {
      const statusType = status.status.toLowerCase();
      if (['completed', 'success', 'submitted'].includes(statusType)) {
        statusCounts.completed++;
      } else if (['processing', 'in_progress'].includes(statusType)) {
        statusCounts.processing++;
      } else if (['failed', 'error'].includes(statusType)) {
        statusCounts.failed++;
      } else if (['pending', 'queued', 'waiting'].includes(statusType)) {
        statusCounts.pending++;
      } else {
        statusCounts.unknown++;
      }
    });

    return statusCounts;
  };

  const summary = getStatusSummary();
  const hasStatuses = Object.keys(statuses).length > 0;

  return (
    <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Статусы в боте
        </h3>
        <button
          onClick={checkAllStatuses}
          disabled={loading || applicationIds.length === 0}
          className="flex items-center gap-2 px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 transition-colors"
        >
          {loading ? (
            <>
              <svg className="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {progress.current}/{progress.total}
            </>
          ) : (
            <>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Проверить все
            </>
          )}
        </button>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded text-sm text-red-700">
          {error}
        </div>
      )}

      {applicationIds.length === 0 ? (
        <div className="text-sm text-gray-500 italic">
          Нет завершенных заявок для проверки
        </div>
      ) : hasStatuses ? (
        <div className="space-y-3">
          <div className="text-sm text-gray-600">
            Проверено: {Object.keys(statuses).length} из {applicationIds.length}
          </div>
          
          <div className="grid grid-cols-2 gap-2">
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-600">✅ Готово:</span>
              <span className="font-medium text-green-600">{summary.completed}</span>
            </div>
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-600">⚙️ Процесс:</span>
              <span className="font-medium text-yellow-600">{summary.processing}</span>
            </div>
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-600">⏳ Ожидание:</span>
              <span className="font-medium text-blue-600">{summary.pending}</span>
            </div>
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-600">❌ Ошибки:</span>
              <span className="font-medium text-red-600">{summary.failed}</span>
            </div>
          </div>

          {summary.unknown > 0 && (
            <div className="text-xs text-gray-500">
              Неизвестных статусов: {summary.unknown}
            </div>
          )}
        </div>
      ) : (
        <div className="text-sm text-gray-500 italic">
          Нажмите "Проверить все" для получения статусов
        </div>
      )}
    </div>
  );
}