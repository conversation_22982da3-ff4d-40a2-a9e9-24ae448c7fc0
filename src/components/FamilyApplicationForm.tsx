"use client"

import { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { visaApplicationSchema } from '@/lib/validation';

interface FamilyApplicationFormProps {
  onToggleMode: () => void;
}

export default function FamilyApplicationForm({ onToggleMode }: FamilyApplicationFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');
  const [submitStatus, setSubmitStatus] = useState<'success' | 'error' | null>(null);

  const {
    register,
    handleSubmit,
    control,
    watch,
    reset,
    formState: { errors, isValid }
  } = useForm({
    resolver: zodResolver(visaApplicationSchema),
    defaultValues: {
      isFamily: true,
      applicants: [{
        firstName: '',
        lastName: '',
        middleName: '',
        dateOfBirth: '',
        gender: 'Male' as const,
        passportNumber: '',
        passportExpiry: '',
        nationality: 'KAZAKHSTAN',
        isEndorsedChild: false,
        relationshipToMain: 'main' as const,
      }]
    }
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "applicants"
  });

  const dateFlexibility = watch('dateFlexibility');

  const addApplicant = (relationship: 'spouse' | 'child' | 'other') => {
    if (fields.length >= 5) {
      alert('Максимум 5 заявителей в семейной заявке');
      return;
    }

    append({
      firstName: '',
      lastName: '',
      middleName: '',
      dateOfBirth: '',
      gender: 'Male' as const,
      passportNumber: '',
      passportExpiry: '',
      nationality: 'KAZAKHSTAN',
      isEndorsedChild: relationship === 'child',
      relationshipToMain: relationship,
      parentPassportNumber: relationship === 'child' ? fields[0]?.passportNumber || '' : undefined,
      parentPassportExpiry: relationship === 'child' ? fields[0]?.passportExpiry || '' : undefined,
    });
  };

  const onSubmit = async (data: any) => {
    setIsSubmitting(true);
    setSubmitMessage('');
    setSubmitStatus(null);

    try {
      const response = await fetch('/api/applications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok) {
        setSubmitMessage(`✅ Семейная заявка успешно принята! 
          
          Номер заявки: ${result.id}
          Количество заявителей: ${data.applicants.length}
          
          Мы начнем поиск доступных слотов для записи на подачу документов для всей семьи.
          Вы получите уведомление на указанный email и телефон.`);
        setSubmitStatus('success');
        
        window.scrollTo({ top: 0, behavior: 'smooth' });
        
        setTimeout(() => {
          reset();
          setSubmitMessage('');
          setSubmitStatus(null);
        }, 10000);
      } else {
        setSubmitMessage(`❌ Произошла ошибка при отправке заявки. 
          
          ${result.error || 'Пожалуйста, проверьте данные и попробуйте снова.'}`);
        setSubmitStatus('error');
      }
    } catch (error) {
      console.error('Error submitting application:', error);
      setSubmitMessage('❌ Произошла ошибка соединения. Пожалуйста, проверьте интернет-соединение и попробуйте снова.');
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getRelationshipLabel = (relationship: string) => {
    switch (relationship) {
      case 'main':
        return 'Основной заявитель';
      case 'spouse':
        return 'Супруг/супруга';  
      case 'child':
        return 'Ребенок';
      case 'other':
        return 'Другое';
      default:
        return 'Не указано';
    }
  };

  const getRelationshipIcon = (relationship: string) => {
    switch (relationship) {
      case 'main':
        return '👤';
      case 'spouse':
        return '💑';
      case 'child':
        return '👶';
      case 'other':
        return '👥';
      default:
        return '❓';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header with Application Type Switcher */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-red-500 rounded-full flex items-center justify-center shadow-lg">
              <span className="text-2xl">🇮🇹</span>
            </div>
          </div>
          
          <h1 className="text-4xl font-bold text-gray-900 mb-3">Заявка на визу в Италию</h1>
          <p className="text-lg text-gray-600 mb-8">Выберите тип заявки для оформления визы</p>
          
          {/* Application Type Toggle - More Prominent */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-12">
            <button
              onClick={onToggleMode}
              className="bg-white rounded-2xl p-6 shadow-lg border-2 border-blue-200 hover:border-blue-400 hover:shadow-xl transition-all duration-300 max-w-sm w-full group"
            >
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-colors">
                  <span className="text-2xl">👤</span>
                </div>
                <h3 className="text-xl font-bold text-blue-900 mb-2">Индивидуальная заявка</h3>
                <p className="text-sm text-gray-600 mb-4">Для одного человека с расширенными VFS полями</p>
                <div className="bg-blue-500 text-white px-4 py-2 rounded-lg font-medium group-hover:bg-blue-600 transition-colors">
                  ← Перейти к индивидуальной
                </div>
              </div>
            </button>
            
            <div className="text-2xl text-gray-400 rotate-90 sm:rotate-0">⟷</div>
            
            <div className="bg-white rounded-2xl p-6 shadow-xl border-2 border-purple-200 max-w-sm w-full">
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">👨‍👩‍👧‍👦</span>
                </div>
                <h3 className="text-xl font-bold text-purple-900 mb-2">Семейная заявка</h3>
                <p className="text-sm text-gray-600 mb-4">Для семьи до 5 человек с поддержкой детей</p>
                <div className="bg-purple-500 text-white px-4 py-2 rounded-lg font-medium">
                  ✓ Текущий выбор
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Form Container */}
        <div className="bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden">
          <div className="bg-gradient-to-r from-purple-600 to-purple-700 px-8 py-6">
            <h2 className="text-2xl font-bold text-white flex items-center">
              <span className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                👨‍👩‍👧‍👦
              </span>
              Семейная заявка
            </h2>
            <p className="text-purple-100 mt-2">Заполните данные для всех членов семьи</p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-8 space-y-10">
            {submitMessage && (
              <div className={`mb-6 p-4 rounded-lg border ${
                submitStatus === 'success' 
                  ? 'bg-green-50 border-green-200 text-green-800' 
                  : 'bg-red-50 border-red-200 text-red-800'
              }`}>
                <pre className="whitespace-pre-wrap text-sm">{submitMessage}</pre>
              </div>
            )}
        {/* Family Members */}
        <section className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold border-b pb-2">Члены семьи</h2>
            <div className="flex gap-2">
              <button
                type="button"
                onClick={() => addApplicant('spouse')}
                disabled={fields.length >= 5}
                className="px-3 py-1 text-xs bg-purple-100 text-purple-700 rounded-full hover:bg-purple-200 disabled:opacity-50 transition-colors"
              >
                + Супруг/а
              </button>
              <button
                type="button"
                onClick={() => addApplicant('child')}
                disabled={fields.length >= 5}
                className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 disabled:opacity-50 transition-colors"
              >
                + Ребенок
              </button>
            </div>
          </div>

          {fields.map((field, index) => (
            <div key={field.id} className="border border-gray-200 rounded-xl p-6 relative">
              {index > 0 && (
                <button
                  type="button"
                  onClick={() => remove(index)}
                  className="absolute top-4 right-4 p-1 text-red-500 hover:text-red-700 transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              )}

              <div className="flex items-center gap-2 mb-4">
                <span className="text-lg">{getRelationshipIcon(field.relationshipToMain)}</span>
                <h3 className="text-lg font-semibold text-gray-900">
                  {getRelationshipLabel(field.relationshipToMain)} {index + 1}
                </h3>
                {field.relationshipToMain === 'child' && (
                  <span className="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">
                    До 12 лет
                  </span>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Имя *</label>
                  <input
                    {...register(`applicants.${index}.firstName`)}
                    type="text"
                    placeholder="FIRSTNAME"
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  {errors.applicants?.[index]?.firstName && (
                    <p className="text-red-500 text-sm mt-1">{errors.applicants[index]?.firstName?.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Фамилия *</label>
                  <input
                    {...register(`applicants.${index}.lastName`)}
                    type="text"
                    placeholder="LASTNAME"
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  {errors.applicants?.[index]?.lastName && (
                    <p className="text-red-500 text-sm mt-1">{errors.applicants[index]?.lastName?.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Отчество (если есть)</label>
                  <input
                    {...register(`applicants.${index}.middleName`)}
                    type="text"
                    placeholder="MIDDLENAME"
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  {errors.applicants?.[index]?.middleName && (
                    <p className="text-red-500 text-sm mt-1">{errors.applicants[index]?.middleName?.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Дата рождения *</label>
                  <input
                    {...register(`applicants.${index}.dateOfBirth`)}
                    type="date"
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  {errors.applicants?.[index]?.dateOfBirth && (
                    <p className="text-red-500 text-sm mt-1">{errors.applicants[index]?.dateOfBirth?.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Пол *</label>
                  <select
                    {...register(`applicants.${index}.gender`)}
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="Male">Мужской</option>
                    <option value="Female">Женский</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Номер паспорта *</label>
                  <input
                    {...register(`applicants.${index}.passportNumber`)}
                    type="text"
                    placeholder="N12345678"
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  {errors.applicants?.[index]?.passportNumber && (
                    <p className="text-red-500 text-sm mt-1">{errors.applicants[index]?.passportNumber?.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Срок действия паспорта *</label>
                  <input
                    {...register(`applicants.${index}.passportExpiry`)}
                    type="date"
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  {errors.applicants?.[index]?.passportExpiry && (
                    <p className="text-red-500 text-sm mt-1">{errors.applicants[index]?.passportExpiry?.message}</p>
                  )}
                </div>

                {/* Child-specific fields */}
                {field.relationshipToMain === 'child' && (
                  <>
                    <div>
                      <label className="block text-sm font-medium mb-2">Паспорт родителя *</label>
                      <input
                        {...register(`applicants.${index}.parentPassportNumber`)}
                        type="text"
                        placeholder="N12345678"
                        className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                      {errors.applicants?.[index]?.parentPassportNumber && (
                        <p className="text-red-500 text-sm mt-1">{errors.applicants[index]?.parentPassportNumber?.message}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">Срок действия паспорта родителя *</label>
                      <input
                        {...register(`applicants.${index}.parentPassportExpiry`)}
                        type="date"
                        className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                      {errors.applicants?.[index]?.parentPassportExpiry && (
                        <p className="text-red-500 text-sm mt-1">{errors.applicants[index]?.parentPassportExpiry?.message}</p>
                      )}
                    </div>
                  </>
                )}
              </div>
            </div>
          ))}
        </section>

        {/* Contact Info (from main applicant) */}
        <section className="space-y-6">
          <h2 className="text-xl font-semibold border-b pb-2">Контактная информация</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium mb-2">Номер телефона *</label>
              <input
                {...register('phoneNumber')}
                type="tel"
                placeholder="+77001234567"
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              {errors.phoneNumber && (
                <p className="text-red-500 text-sm mt-1">{errors.phoneNumber.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Email *</label>
              <input
                {...register('email')}
                type="email"
                placeholder="<EMAIL>"
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              {errors.email && (
                <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
              )}
            </div>
          </div>
        </section>

        {/* Visa Preferences */}
        <section className="space-y-6">
          <h2 className="text-xl font-semibold border-b pb-2">Предпочтения по визе</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium mb-2">Категория визы *</label>
              <select
                {...register('visaCategory')}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Выберите категорию</option>
                <option value="TOURISM">Туризм</option>
                <option value="BUSINESS">Бизнес</option>
                <option value="FAMILY">Семейная</option>
                <option value="STUDY">Обучение</option>
              </select>
              {errors.visaCategory && (
                <p className="text-red-500 text-sm mt-1">{errors.visaCategory.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Цель поездки *</label>
              <select
                {...register('visaSubcategory')}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Выберите цель</option>
                <option value="INDIVIDUAL">Индивидуальная поездка</option>
                <option value="GROUP">Групповая поездка</option>
                <option value="FAMILY_VISIT">Посещение семьи</option>
                <option value="BUSINESS_MEETING">Деловая встреча</option>
              </select>
              {errors.visaSubcategory && (
                <p className="text-red-500 text-sm mt-1">{errors.visaSubcategory.message}</p>
              )}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Предпочитаемые визовые центры *</label>
            <div className="grid grid-cols-2 gap-4">
              {['ALMATY', 'ASTANA', 'SHYMKENT'].map((center) => (
                <label key={center} className="flex items-center">
                  <input
                    {...register('preferredCenters')}
                    type="checkbox"
                    value={center}
                    className="mr-2"
                  />
                  {center}
                </label>
              ))}
            </div>
            {errors.preferredCenters && (
              <p className="text-red-500 text-sm mt-1">{errors.preferredCenters.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Гибкость по датам *</label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  {...register('dateFlexibility')}
                  type="radio"
                  value="any_date"
                  className="mr-2"
                />
                Любая доступная дата
              </label>
              <label className="flex items-center">
                <input
                  {...register('dateFlexibility')}
                  type="radio"
                  value="date_range"
                  className="mr-2"
                />
                Конкретный диапазон дат
              </label>
            </div>
            {errors.dateFlexibility && (
              <p className="text-red-500 text-sm mt-1">{errors.dateFlexibility.message}</p>
            )}
          </div>

          {dateFlexibility === 'date_range' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium mb-2">Дата начала</label>
                <input
                  {...register('preferredDateRange.from')}
                  type="date"
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Дата окончания</label>
                <input
                  {...register('preferredDateRange.to')}
                  type="date"
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          )}
        </section>



        <div className="pt-6">
          <button
            type="submit"
            disabled={isSubmitting || !isValid}
            className="w-full bg-blue-600 text-white py-4 px-6 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-lg font-semibold"
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Отправка семейной заявки...
              </div>
            ) : (
              `Подать семейную заявку (${fields.length} ${fields.length === 1 ? 'человек' : fields.length < 5 ? 'человека' : 'человек'})`
            )}
          </button>
          </div>
          </form>
        </div>
      </div>
    </div>
  );
}