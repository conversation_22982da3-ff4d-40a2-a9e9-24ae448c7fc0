"use client"

import { useState } from 'react';
import ApplicationStatusChecker from './ApplicationStatusChecker';

// Utility function to safely parse JSON fields
const safeJsonParse = (value: any, fallback: any = null) => {
  if (Array.isArray(value)) return value;
  if (typeof value === 'string') {
    try {
      return JSON.parse(value);
    } catch {
      return fallback;
    }
  }
  return fallback;
};

interface ApplicationModalProps {
  application: any;
  isOpen: boolean;
  onClose: () => void;
}

export default function ApplicationModalV2({ application, isOpen, onClose }: ApplicationModalProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'details' | 'system'>('overview');

  if (!isOpen || !application) return null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'NEW':
        return 'bg-blue-500';
      case 'PAID':
        return 'bg-emerald-500';
      case 'COMPLETED':
        return 'bg-gray-400';
      default:
        return 'bg-gray-400';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'NEW':
        return 'Новая';
      case 'PAID':
        return 'Оплаченная';
      case 'COMPLETED':
        return 'Завершенная';
      default:
        return status;
    }
  };


  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Backdrop */}
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity backdrop-blur-sm" 
          aria-hidden="true"
          onClick={onClose}
        />

        {/* Modal */}
        <div className="inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-6 text-white">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h2 className="text-2xl font-bold">{application.fullName}</h2>
                </div>
                
                <div className="flex items-center gap-4 text-blue-100">
                  <div className="flex items-center gap-2">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
                    </svg>
                    {application.passportNumber}
                  </div>
                  <div className="flex items-center gap-2">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    {new Date(application.createdAt).toLocaleDateString('ru-RU')}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium text-white ${getStatusColor(application.status)}`}>
                  {getStatusLabel(application.status)}
                </span>
                
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="flex space-x-0">
              <button
                onClick={() => setActiveTab('overview')}
                className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === 'overview'
                    ? 'border-blue-500 text-blue-600 bg-blue-50'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  Обзор
                </div>
              </button>
              
              <button
                onClick={() => setActiveTab('details')}
                className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === 'details'
                    ? 'border-blue-500 text-blue-600 bg-blue-50'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Детали визы
                </div>
              </button>
              
              <button
                onClick={() => setActiveTab('system')}
                className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === 'system'
                    ? 'border-blue-500 text-blue-600 bg-blue-50'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  Система
                </div>
              </button>
            </nav>
          </div>

          {/* Content */}
          <div className="max-h-96 overflow-y-auto">
            {activeTab === 'overview' && (
              <div className="p-6 space-y-6">
                {/* Personal Information Card */}
                <div className="bg-gray-50 rounded-xl p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    Личная информация
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Полное имя</label>
                        <p className="text-lg font-semibold text-gray-900 mt-1">{application.fullName}</p>
                      </div>
                      
                      <div>
                        <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Дата рождения</label>
                        <p className="text-sm text-gray-900 mt-1">{new Date(application.dateOfBirth).toLocaleDateString('ru-RU')}</p>
                      </div>
                      
                      <div>
                        <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Национальность</label>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-lg">🇰🇿</span>
                          <p className="text-sm text-gray-900">{application.nationality}</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-4">
                      <div>
                        <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Пол</label>
                        <p className="text-sm text-gray-900 mt-1">
                          {application.gender === 'Male' ? '👨 Мужской' : '👩 Женский'}
                        </p>
                      </div>
                      
                    </div>
                  </div>
                </div>

                {/* Contact Information Card */}
                <div className="bg-gray-50 rounded-xl p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                    Контактная информация
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Телефон</label>
                      <div className="flex items-center justify-between mt-1">
                        <p className="text-sm font-mono text-gray-900">{application.phoneNumber}</p>
                        <button
                          onClick={() => copyToClipboard(application.phoneNumber)}
                          className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                          title="Копировать"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                        </button>
                      </div>
                    </div>
                    
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Email</label>
                      <div className="flex items-center justify-between mt-1">
                        <p className="text-sm text-gray-900">{application.email}</p>
                        <button
                          onClick={() => copyToClipboard(application.email)}
                          className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                          title="Копировать"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Passport Information Card */}
                <div className="bg-gray-50 rounded-xl p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
                    </svg>
                    Документы
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Номер паспорта</label>
                      <div className="flex items-center justify-between mt-1">
                        <p className="text-sm font-mono text-gray-900 bg-white px-3 py-1 rounded-lg border">
                          {application.passportNumber}
                        </p>
                        <button
                          onClick={() => copyToClipboard(application.passportNumber)}
                          className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                          title="Копировать"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                        </button>
                      </div>
                    </div>
                    
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Срок действия</label>
                      <p className="text-sm text-gray-900 mt-1 bg-white px-3 py-1 rounded-lg border">
                        {new Date(application.passportExpiry).toLocaleDateString('ru-RU')}
                      </p>
                    </div>
                  </div>

                  {/* Passport validity check */}
                  <div className="mt-4">
                    {new Date(application.passportExpiry) > new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000) ? (
                      <div className="flex items-center gap-2 text-green-700 bg-green-100 px-3 py-2 rounded-lg">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Паспорт действителен более 6 месяцев
                      </div>
                    ) : (
                      <div className="flex items-center gap-2 text-red-700 bg-red-100 px-3 py-2 rounded-lg">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Внимание: Паспорт может истечь менее чем через 6 месяцев
                      </div>
                    )}
                  </div>
                </div>

                {/* Error Display */}
                {application.error && (
                  <div className="bg-red-50 border border-red-200 rounded-xl p-6">
                    <h3 className="text-lg font-semibold text-red-900 mb-2 flex items-center gap-2">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Ошибка обработки
                    </h3>
                    <p className="text-sm text-red-700 leading-relaxed">{application.error}</p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'details' && (
              <div className="p-6 space-y-6">
                {/* Visa Preferences */}
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Детали визы
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Категория визы</label>
                      <p className="text-sm font-semibold text-gray-900 mt-1 bg-white px-3 py-2 rounded-lg border">
                        {application.visaCategory}
                      </p>
                    </div>
                    
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Цель поездки</label>
                      <p className="text-sm font-semibold text-gray-900 mt-1 bg-white px-3 py-2 rounded-lg border">
                        {application.visaSubcategory}
                      </p>
                    </div>
                    
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Гибкость по датам</label>
                      <p className="text-sm text-gray-900 mt-1 bg-white px-3 py-2 rounded-lg border">
                        {application.dateFlexibility === 'any_date' ? '✅ Любая дата' : '📅 Конкретный диапазон'}
                      </p>
                    </div>
                  </div>

                  {application.preferredDateRange && (
                    <div className="mt-4">
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Предпочитаемый диапазон дат</label>
                      <div className="mt-2 bg-white p-4 rounded-lg border">
                        <div className="flex items-center gap-4">
                          <div>
                            <span className="text-xs text-gray-500">С</span>
                            <p className="text-sm font-medium">{application.preferredDateRange.from || 'Не указано'}</p>
                          </div>
                          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                          </svg>
                          <div>
                            <span className="text-xs text-gray-500">По</span>
                            <p className="text-sm font-medium">{application.preferredDateRange.to || 'Не указано'}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Preferred Centers */}
                <div className="bg-gray-50 rounded-xl p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    Предпочитаемые центры
                  </h3>
                  
                  {(() => {
                    const centers = safeJsonParse(application.preferredCenters, []);
                    return centers && centers.length > 0 ? (
                      <div className="flex flex-wrap gap-2">
                        {centers.map((center: string, index: number) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800"
                          >
                            📍 {center}
                          </span>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500 italic">Центры не указаны</p>
                    );
                  })()}
                </div>


                {/* Comments */}
                {application.comments && (
                  <div className="bg-gray-50 rounded-xl p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                      Комментарии
                    </h3>
                    <div className="bg-white p-4 rounded-lg border">
                      <p className="text-sm text-gray-700 leading-relaxed">{application.comments}</p>
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'system' && (
              <div className="p-6 space-y-6">
                {/* Bot Status for Completed Applications */}
                {application.status === 'COMPLETED' && (
                  <div className="mb-6">
                    <ApplicationStatusChecker applicationId={application.id} />
                  </div>
                )}

                {/* System Information */}
                <div className="bg-gray-50 rounded-xl p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Системная информация
                  </h3>
                  
                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">ID заявки</label>
                      <div className="flex items-center justify-between mt-1">
                        <p className="text-sm font-mono text-gray-900 bg-white px-3 py-2 rounded-lg border flex-1">
                          {application.id}
                        </p>
                        <button
                          onClick={() => copyToClipboard(application.id)}
                          className="ml-2 p-2 text-gray-400 hover:text-gray-600 transition-colors"
                          title="Копировать ID"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                        </button>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Дата создания</label>
                        <p className="text-sm text-gray-900 mt-1 bg-white px-3 py-2 rounded-lg border">
                          {new Date(application.createdAt).toLocaleString('ru-RU')}
                        </p>
                      </div>
                      
                      <div>
                        <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Последнее обновление</label>
                        <p className="text-sm text-gray-900 mt-1 bg-white px-3 py-2 rounded-lg border">
                          {new Date(application.updatedAt).toLocaleString('ru-RU')}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* VFS System Codes */}
                <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                    VFS System Codes
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Country/Mission</label>
                      <p className="text-sm text-gray-900 mt-1 bg-white px-3 py-2 rounded-lg border">
                        {application.countryCode}/{application.missionCode} ({application.nationalityCode})
                      </p>
                    </div>
                    
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">VFS Center</label>
                      <p className="text-sm text-gray-900 mt-1 bg-white px-3 py-2 rounded-lg border">
                        {application.vfsCenterCode || 'Not set'}
                      </p>
                    </div>
                    
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Visa Category</label>
                      <p className="text-sm text-gray-900 mt-1 bg-white px-3 py-2 rounded-lg border">
                        {application.vfsVisaCategoryCode}/{application.vfsVisaSubcategoryCode}
                      </p>
                    </div>
                    
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Payment</label>
                      <p className="text-sm text-gray-900 mt-1 bg-white px-3 py-2 rounded-lg border">
                        {application.amount} {application.currencyCode} ({application.paymentMode})
                      </p>
                    </div>
                    
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Language/Dial</label>
                      <p className="text-sm text-gray-900 mt-1 bg-white px-3 py-2 rounded-lg border">
                        {application.languageCode} / +{application.dialCode}
                      </p>
                    </div>
                    
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Role/Type</label>
                      <p className="text-sm text-gray-900 mt-1 bg-white px-3 py-2 rounded-lg border">
                        {application.roleName} (Type: {application.applicantType})
                      </p>
                    </div>
                  </div>
                </div>

              </div>
            )}
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-6 py-4 flex items-center justify-between border-t">
            <div className="text-sm text-gray-500">
              Заявка обновлена: {new Date(application.updatedAt).toLocaleString('ru-RU')}
            </div>
            
            <div className="flex items-center gap-3">
              <button
                onClick={() => copyToClipboard(JSON.stringify(application, null, 2))}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                Копировать JSON
              </button>
              
              <button
                onClick={onClose}
                className="inline-flex items-center px-6 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
              >
                Закрыть
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}