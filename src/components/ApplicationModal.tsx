"use client"


interface ApplicationModalProps {
  application: any;
  isOpen: boolean;
  onClose: () => void;
}

export default function ApplicationModal({ application, isOpen, onClose }: ApplicationModalProps) {
  if (!isOpen || !application) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div 
          className="fixed inset-0 transition-opacity" 
          aria-hidden="true"
          onClick={onClose}
        >
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Детали заявки
                </h3>
                
                <div className="mt-4 space-y-6">
                  {/* Personal Info */}
                  <div>
                    <h4 className="text-md font-semibold text-gray-700 mb-2">Личная информация</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-500">Полное имя</p>
                        <p className="text-sm font-medium">{application.fullName}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Дата рождения</p>
                        <p className="text-sm font-medium">{new Date(application.dateOfBirth).toLocaleDateString('ru-RU')}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Пол</p>
                        <p className="text-sm font-medium">{application.gender === 'Male' ? 'Мужской' : 'Женский'}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Статус</p>
                        <p className="text-sm font-medium">{application.status}</p>
                      </div>
                    </div>
                  </div>

                  {/* Passport Info */}
                  <div>
                    <h4 className="text-md font-semibold text-gray-700 mb-2">Паспортные данные</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-500">Номер паспорта</p>
                        <p className="text-sm font-medium">{application.passportNumber}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Срок действия</p>
                        <p className="text-sm font-medium">{new Date(application.passportExpiry).toLocaleDateString('ru-RU')}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Национальность</p>
                        <p className="text-sm font-medium">{application.nationality}</p>
                      </div>
                    </div>
                  </div>

                  {/* Contact Info */}
                  <div>
                    <h4 className="text-md font-semibold text-gray-700 mb-2">Контактная информация</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-500">Телефон</p>
                        <p className="text-sm font-medium">{application.phoneNumber}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Email</p>
                        <p className="text-sm font-medium">{application.email}</p>
                      </div>
                    </div>
                  </div>

                  {/* Visa Preferences */}
                  <div>
                    <h4 className="text-md font-semibold text-gray-700 mb-2">Предпочтения по визе</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-500">Категория визы</p>
                        <p className="text-sm font-medium">{application.visaCategory}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Цель поездки</p>
                        <p className="text-sm font-medium">{application.visaSubcategory}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Срочность</p>
                        <p className="text-sm font-medium">
                          {application.urgency === 'very_urgent' ? 'Очень срочная' : 
                           application.urgency === 'urgent' ? 'Срочная' : 'Обычная'}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Гибкость по датам</p>
                        <p className="text-sm font-medium">
                          {application.dateFlexibility === 'any_date' ? 'Любая дата' : 'Диапазон дат'}
                        </p>
                      </div>
                    </div>
                    
                    {application.preferredCenters && (
                      <div className="mt-2">
                        <p className="text-sm text-gray-500">Предпочитаемые центры</p>
                        <p className="text-sm font-medium">{application.preferredCenters.join(', ')}</p>
                      </div>
                    )}

                    {application.preferredDateRange && (
                      <div className="mt-2">
                        <p className="text-sm text-gray-500">Предпочитаемый диапазон дат</p>
                        <p className="text-sm font-medium">
                          {application.preferredDateRange.from || 'Не указано'} - {application.preferredDateRange.to || 'Не указано'}
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Additional Options */}
                  {application.alternativeOptions && application.alternativeOptions.length > 0 && (
                    <div>
                      <h4 className="text-md font-semibold text-gray-700 mb-2">Дополнительные опции</h4>
                      <ul className="text-sm text-gray-600 list-disc list-inside">
                        {application.alternativeOptions.includes('consider_long_stay') && (
                          <li>Рассмотреть Long Stay визу если нет Short Stay</li>
                        )}
                        {application.alternativeOptions.includes('any_city') && (
                          <li>Готов поехать в любой город Казахстана</li>
                        )}
                        {application.alternativeOptions.includes('notify_availability') && (
                          <li>Уведомить когда появятся слоты</li>
                        )}
                      </ul>
                    </div>
                  )}

                  {/* Comments */}
                  {application.comments && (
                    <div>
                      <h4 className="text-md font-semibold text-gray-700 mb-2">Комментарии</h4>
                      <p className="text-sm text-gray-600">{application.comments}</p>
                    </div>
                  )}

                  {/* Error */}
                  {application.error && (
                    <div>
                      <h4 className="text-md font-semibold text-red-700 mb-2">Ошибка</h4>
                      <p className="text-sm text-red-600">{application.error}</p>
                    </div>
                  )}

                  {/* System Info */}
                  <div>
                    <h4 className="text-md font-semibold text-gray-700 mb-2">Системная информация</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-500">ID заявки</p>
                        <p className="text-sm font-medium font-mono">{application.id}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Дата создания</p>
                        <p className="text-sm font-medium">
                          {new Date(application.createdAt).toLocaleString('ru-RU')}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Последнее обновление</p>
                        <p className="text-sm font-medium">
                          {new Date(application.updatedAt).toLocaleString('ru-RU')}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
              onClick={onClose}
            >
              Закрыть
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}