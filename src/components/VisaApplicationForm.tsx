"use client"

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { visaApplicationSchema, VisaApplicationFormData } from '@/lib/validation';

export default function VisaApplicationForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');
  const [submitStatus, setSubmitStatus] = useState<'success' | 'error' | null>(null);

  const {
    register,
    handleSubmit,
    watch,
    reset,
    setValue,
    formState: { errors }
  } = useForm<VisaApplicationFormData>({
    resolver: zodResolver(visaApplicationSchema),
    defaultValues: {}
  });

  const dateFlexibility = watch('dateFlexibility');
  const firstName = watch('firstName');
  const lastName = watch('lastName');
  const middleName = watch('middleName');

  // Auto-fill fullName when individual name fields change
  useEffect(() => {
    if (firstName && lastName) {
      const fullName = middleName 
        ? `${firstName} ${middleName} ${lastName}`.trim()
        : `${firstName} ${lastName}`.trim();
      setValue('fullName', fullName);
    }
  }, [firstName, lastName, middleName, setValue]);

  const onSubmit = async (data: VisaApplicationFormData) => {
    setIsSubmitting(true);
    setSubmitMessage('');
    setSubmitStatus(null);

    try {
      const response = await fetch('/api/applications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok) {
        setSubmitMessage(`✅ Ваша заявка успешно принята! 
          
          Номер заявки: ${result.id}
          
          Мы начнем поиск доступных слотов для записи на подачу документов.
          Вы получите уведомление на указанный email и телефон.`);
        setSubmitStatus('success');
        
        // Scroll to top to show success message
        window.scrollTo({ top: 0, behavior: 'smooth' });
        
        // Reset form after successful submission
        setTimeout(() => {
          reset();
          setSubmitMessage('');
          setSubmitStatus(null);
        }, 10000); // Clear after 10 seconds
      } else {
        setSubmitMessage(`❌ Произошла ошибка при отправке заявки. 
          
          ${result.error || 'Пожалуйста, проверьте данные и попробуйте снова.'}`);
        setSubmitStatus('error');
      }
    } catch (error) {
      console.error('Error submitting application:', error);
      setSubmitMessage('❌ Произошла ошибка соединения. Пожалуйста, проверьте интернет-соединение и попробуйте снова.');
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header with Application Type Switcher */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-red-500 rounded-full flex items-center justify-center shadow-lg">
              <span className="text-2xl">🇮🇹</span>
            </div>
          </div>
          
          <h1 className="text-4xl font-bold text-gray-900 mb-3">Заявка на визу в Италию</h1>
          <p className="text-lg text-gray-600 mb-8">Выберите тип заявки для оформления визы</p>
          
          {/* Application Type Toggle - More Prominent */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-12">
            <div className="bg-white rounded-2xl p-6 shadow-xl border-2 border-blue-200 max-w-sm w-full">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">👤</span>
                </div>
                <h3 className="text-xl font-bold text-blue-900 mb-2">Индивидуальная заявка</h3>
                <p className="text-sm text-gray-600 mb-4">Для одного человека с расширенными VFS полями</p>
                <div className="bg-blue-500 text-white px-4 py-2 rounded-lg font-medium">
                  ✓ Текущий выбор
                </div>
              </div>
            </div>
            
            <div className="text-2xl text-gray-400 rotate-90 sm:rotate-0">⟷</div>
            
            <a
              href="/family-application"
              className="bg-white rounded-2xl p-6 shadow-lg border-2 border-purple-200 hover:border-purple-400 hover:shadow-xl transition-all duration-300 max-w-sm w-full group"
            >
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-200 transition-colors">
                  <span className="text-2xl">👨‍👩‍👧‍👦</span>
                </div>
                <h3 className="text-xl font-bold text-purple-900 mb-2">Семейная заявка</h3>
                <p className="text-sm text-gray-600 mb-4">Для семьи до 5 человек с поддержкой детей</p>
                <div className="bg-purple-500 text-white px-4 py-2 rounded-lg font-medium group-hover:bg-purple-600 transition-colors">
                  Перейти к семейной →
                </div>
              </div>
            </a>
          </div>
        </div>

        {/* Form Container */}
        <div className="bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden">
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-8 py-6">
            <h2 className="text-2xl font-bold text-white flex items-center">
              <span className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                📝
              </span>
              Индивидуальная заявка
            </h2>
            <p className="text-blue-100 mt-2">Заполните все поля для подачи заявки на визу</p>
          </div>
      
          <form onSubmit={handleSubmit(onSubmit)} className="p-8 space-y-10">
            {/* Personal Info */}
            <section className="space-y-6">
              <div className="flex items-center mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mr-4">
                  <span className="text-white font-bold">1</span>
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-800">Личная информация</h2>
                  <p className="text-gray-600">Введите ваши персональные данные</p>
                </div>
              </div>
          
              <div className="bg-gray-50 rounded-2xl p-6 border border-gray-200">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center">
                      <span className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                        👤
                      </span>
                      Имя (как в паспорте) *
                    </label>
                    <input
                      {...register('firstName')}
                      type="text"
                      placeholder="FIRSTNAME"
                      className="w-full p-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all duration-200 font-medium"
                    />
                    {errors.firstName && (
                      <p className="text-red-500 text-sm mt-2 flex items-center">
                        <span className="mr-1">⚠️</span>
                        {errors.firstName.message}
                      </p>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center">
                      <span className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                        👤
                      </span>
                      Фамилия (как в паспорте) *
                    </label>
                    <input
                      {...register('lastName')}
                      type="text"
                      placeholder="LASTNAME"
                      className="w-full p-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all duration-200 font-medium"
                    />
                    {errors.lastName && (
                      <p className="text-red-500 text-sm mt-2 flex items-center">
                        <span className="mr-1">⚠️</span>
                        {errors.lastName.message}
                      </p>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center">
                      <span className="w-5 h-5 bg-gray-100 rounded-full flex items-center justify-center mr-2">
                        👤
                      </span>
                      Отчество (если есть)
                    </label>
                    <input
                      {...register('middleName')}
                      type="text"
                      placeholder="MIDDLENAME"
                      className="w-full p-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all duration-200 font-medium"
                    />
                    {errors.middleName && (
                      <p className="text-red-500 text-sm mt-2 flex items-center">
                        <span className="mr-1">⚠️</span>
                        {errors.middleName.message}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <div className="bg-green-50 rounded-2xl p-6 border border-green-200">
                <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center">
                  <span className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mr-2">
                    ✨
                  </span>
                  Полное имя (как в паспорте) *
                </label>
                <input
                  {...register('fullName')}
                  type="text"
                  placeholder="FIRSTNAME LASTNAME или автоматически из полей выше"
                  className="w-full p-4 border-2 border-green-200 rounded-xl focus:ring-4 focus:ring-green-100 focus:border-green-500 transition-all duration-200 font-medium bg-white"
                  readOnly={!!(firstName && lastName)}
                />
                {errors.fullName && (
                  <p className="text-red-500 text-sm mt-2 flex items-center">
                    <span className="mr-1">⚠️</span>
                    {errors.fullName.message}
                  </p>
                )}
                <div className="mt-3 p-3 bg-white rounded-lg border border-green-200">
                  <p className="text-xs text-green-700 flex items-center">
                    <span className="mr-1">💡</span>
                    {firstName && lastName ? 
                      'Автоматически сформировано из полей выше' : 
                      'Заполните поля имени выше для автоматического формирования'
                    }
                  </p>
                </div>
              </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Дата рождения *
            </label>
            <input
              {...register('dateOfBirth')}
              type="date"
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            {errors.dateOfBirth && (
              <p className="text-red-500 text-sm mt-1">{errors.dateOfBirth.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Пол *</label>
            <select
              {...register('gender')}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Выберите пол</option>
              <option value="Male">Мужской</option>
              <option value="Female">Женский</option>
            </select>
            {errors.gender && (
              <p className="text-red-500 text-sm mt-1">{errors.gender.message}</p>
            )}
          </div>
        </section>

        {/* Passport Info */}
        <section className="space-y-6">
          <h2 className="text-xl font-semibold border-b pb-2">Паспортные данные</h2>
          
          <div>
            <label className="block text-sm font-medium mb-2">
              Номер паспорта *
            </label>
            <input
              {...register('passportNumber')}
              type="text"
              placeholder="N10899680"
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            {errors.passportNumber && (
              <p className="text-red-500 text-sm mt-1">{errors.passportNumber.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Срок действия паспорта *
            </label>
            <input
              {...register('passportExpiry')}
              type="date"
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            {errors.passportExpiry && (
              <p className="text-red-500 text-sm mt-1">{errors.passportExpiry.message}</p>
            )}
          </div>
        </section>

        {/* Contact Info */}
        <section className="space-y-6">
          <h2 className="text-xl font-semibold border-b pb-2">Контактная информация</h2>
          
          <div>
            <label className="block text-sm font-medium mb-2">
              Номер телефона *
            </label>
            <input
              {...register('phoneNumber')}
              type="tel"
              placeholder="****** 908 9900"
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            {errors.phoneNumber && (
              <p className="text-red-500 text-sm mt-1">{errors.phoneNumber.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Email адрес *
            </label>
            <input
              {...register('email')}
              type="email"
              placeholder="<EMAIL>"
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            {errors.email && (
              <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
            )}
          </div>
        </section>

        {/* Visa Preferences */}
        <section className="space-y-6">
          <h2 className="text-xl font-semibold border-b pb-2">Предпочтения по визе</h2>
          
          <div>
            <label className="block text-sm font-medium mb-2">Категория визы *</label>
            <select
              {...register('visaCategory')}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Выберите категорию</option>
              <option value="SHORT_STAY">Краткосрочная виза (до 90 дней)</option>
            </select>
            {errors.visaCategory && (
              <p className="text-red-500 text-sm mt-1">{errors.visaCategory.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Цель поездки *</label>
            <select
              {...register('visaSubcategory')}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Выберите цель</option>
              <option value="Tourist">Туризм</option>
              <option value="Business">Бизнес</option>
              <option value="Private_Visit">Частный визит</option>
              <option value="Other">Другое</option>
            </select>
            {errors.visaSubcategory && (
              <p className="text-red-500 text-sm mt-1">{errors.visaSubcategory.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Предпочитаемые визовые центры *
            </label>
            <div className="space-y-2">
              {[
                { value: "Almaty", label: "Алматы" },
                { value: "Astana", label: "Астана" },
                { value: "Shymkent", label: "Шымкент" },
                { value: "Atyrau", label: "Атырау" },
                { value: "Ust_Kamenogorsk", label: "Усть-Каменогорск" }
              ].map((center) => (
                <label key={center.value} className="flex items-center">
                  <input
                    {...register('preferredCenters')}
                    type="checkbox"
                    value={center.value}
                    className="mr-2"
                  />
                  {center.label}
                </label>
              ))}
            </div>
            {errors.preferredCenters && (
              <p className="text-red-500 text-sm mt-1">{errors.preferredCenters.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Гибкость по датам *</label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  {...register('dateFlexibility')}
                  type="radio"
                  value="any_date"
                  className="mr-2"
                />
                Любая доступная дата
              </label>
              <label className="flex items-center">
                <input
                  {...register('dateFlexibility')}
                  type="radio"
                  value="date_range"
                  className="mr-2"
                />
                Диапазон дат
              </label>
            </div>
            {errors.dateFlexibility && (
              <p className="text-red-500 text-sm mt-1">{errors.dateFlexibility.message}</p>
            )}
          </div>

          {dateFlexibility === 'date_range' && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">От</label>
                <input
                  {...register('preferredDateRange.from')}
                  type="date"
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">До</label>
                <input
                  {...register('preferredDateRange.to')}
                  type="date"
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          )}
        </section>



            {/* Submit Button */}
            <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-2xl p-8 border border-blue-200">
              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-gray-800 mb-2">Готовы подать заявку?</h3>
                <p className="text-gray-600">Проверьте все данные перед отправкой</p>
              </div>
              
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-6 px-8 rounded-2xl font-bold text-lg hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 transition-all duration-200 shadow-xl hover:shadow-2xl"
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-6 w-6 text-white" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Обработка заявки...
                  </div>
                ) : (
                  <div className="flex items-center justify-center">
                    <span className="mr-2">🚀</span>
                    Отправить заявку на визу
                  </div>
                )}
              </button>
              
              <div className="mt-4 flex items-center justify-center text-xs text-gray-500">
                <span className="mr-1">🔒</span>
                Ваши данные защищены и обрабатываются согласно политике конфиденциальности
              </div>
            </div>

            {submitMessage && (
              <div className={`mx-8 mb-8 p-6 rounded-2xl border-2 transition-all duration-300 ${
                submitStatus === 'success'
                  ? 'bg-green-50 border-green-200 text-green-800'
                  : 'bg-red-50 border-red-200 text-red-800'
              }`}>
                <div className="flex items-start">
                  <span className="text-2xl mr-3">
                    {submitStatus === 'success' ? '✅' : '❌'}
                  </span>
                  <div>
                    <h4 className="font-bold text-lg mb-2">
                      {submitStatus === 'success' ? 'Заявка успешно отправлена!' : 'Ошибка при отправке'}
                    </h4>
                    <pre className="whitespace-pre-wrap text-sm leading-relaxed">{submitMessage}</pre>
                    {submitStatus === 'success' && (
                      <div className="mt-4 p-3 bg-white rounded-lg border border-green-300">
                        <p className="text-xs text-green-600 flex items-center">
                          <span className="mr-1">⏱️</span>
                          Эта форма автоматически очистится через 10 секунд для новой заявки.
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </form>
        </div>
      </div>
    </div>
  );
}