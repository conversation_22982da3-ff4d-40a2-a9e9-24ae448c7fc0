"use client"

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import ApplicationModalV2 from './ApplicationModalV2';
import BotStatus from './BotStatus';
import Application<PERSON>tatusChecker from './ApplicationStatusChecker';
import Bulk<PERSON>tatusChecker from './BulkStatusChecker';
import { convertApplicationToBotPayload, validateBotPayload } from '@/lib/botPayloadConverter';

interface VisaApplication {
  id: string;
  isFamily: boolean;
  familySize: number;
  applicants?: any[] | string;
  fullName: string;
  dateOfBirth: string;
  gender: string;
  passportNumber: string;
  passportExpiry: string;
  nationality: string;
  phoneNumber: string;
  email: string;
  visaCategory: string;
  visaSubcategory: string;
  preferredCenters: string[] | string;
  dateFlexibility: string;
  preferredDateRange?: any;
  comments?: string;
  status: 'NEW' | 'PAID' | 'COMPLETED';
  error: string | null;
  createdAt: string;
  updatedAt: string;
  // VFS System Codes
  vfsCenterCode?: string;
  vfsVisaCategoryCode?: string;
  vfsVisaSubcategoryCode?: string;
  amount?: number;
  currencyCode?: string;
  countryCode?: string;
  missionCode?: string;
  nationalityCode?: string;
}

type FilterStatus = 'ALL' | 'NEW' | 'PAID' | 'COMPLETED';

// Utility function to safely parse JSON fields
const safeJsonParse = (value: any, fallback: any = null) => {
  if (Array.isArray(value)) return value;
  if (typeof value === 'string') {
    try {
      return JSON.parse(value);
    } catch {
      return fallback;
    }
  }
  return fallback;
};

export default function AdminPanelV2() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [applications, setApplications] = useState<VisaApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingId, setProcessingId] = useState<string | null>(null);
  const [selectedApplication, setSelectedApplication] = useState<VisaApplication | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<FilterStatus>('ALL');
  // const [sortBy, setSortBy] = useState<'date'>('date');
  
  // Test applications state
  const [testAppsStatus, setTestAppsStatus] = useState<{
    existing: number;
    needed: number;
    month: string;
    canCreate: boolean;
    loading: boolean;
  }>({ existing: 0, needed: 10, month: '', canCreate: true, loading: false });
  const [creatingTestApps, setCreatingTestApps] = useState(false);

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/signin');
      return;
    }

    fetchApplications();
  }, [session, status, router]);

  const fetchApplications = async () => {
    try {
      const response = await fetch('/api/applications');
      if (response.ok) {
        const data = await response.json();
        setApplications(data);
      }
    } catch (error) {
      console.error('Error fetching applications:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateApplicationStatus = async (id: string, newStatus: 'NEW' | 'PAID' | 'COMPLETED', error?: string) => {
    setProcessingId(id);
    try {
      const response = await fetch(`/api/applications/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus, error }),
      });

      if (response.ok) {
        await fetchApplications();
      }
    } catch (error) {
      console.error('Error updating application:', error);
    } finally {
      setProcessingId(null);
    }
  };

  const openApplicationDetails = (app: VisaApplication) => {
    setSelectedApplication(app);
    setIsModalOpen(true);
  };

  const cancelApplication = async (id: string) => {
    setProcessingId(id);
    try {
      const submissionResponse = await fetch(`/api/submissions/${id}`);
      
      if (!submissionResponse.ok) {
        await updateApplicationStatus(id, 'NEW', 'Не найдена информация о подаче заявки');
        return;
      }
      
      const submission = await submissionResponse.json();
      
      await fetch(`/api/submissions/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'CANCELLED',
          notes: `Cancelled at ${new Date().toISOString()}`
        }),
      });
      
      const cancellationData = {
        fullName: submission.application.fullName
      };
      
      console.log(`=== ОТМЕНА ЗАЯВКИ ===
ID заявки: ${id}
ID учетной записи: ${submission.credential.id}

Для отмены использовать:
Email: ${submission.credential.email}
Password: ${submission.credential.password}
${submission.credential.description ? `Описание: ${submission.credential.description}` : ''}

JSON данные для отмены:`, JSON.stringify(cancellationData, null, 2), `

Заявка была подана: ${new Date(submission.submittedAt).toLocaleString('ru-RU')}`);
      
      alert(`Заявка отменена!

Для отмены в визовом центре использовать:
Email: ${submission.credential.email}
Password: ${submission.credential.password}
${submission.credential.description ? `\nОписание: ${submission.credential.description}` : ''}

JSON данные: ${JSON.stringify(cancellationData)}

Заявка была подана: ${new Date(submission.submittedAt).toLocaleString('ru-RU')}
Подробности в консоли.`);
      
      await updateApplicationStatus(id, 'NEW');
      
    } catch (error) {
      console.error('Error cancelling application:', error);
      await updateApplicationStatus(id, 'NEW', 'Ошибка при отмене заявки');
    } finally {
      setProcessingId(null);
    }
  };

  const submitApplication = async (id: string) => {
    setProcessingId(id);
    let botPayload: any = null;
    
    try {
      // Get best available credential based on priority system
      const credentialsResponse = await fetch('/api/credentials/best-available');
      
      if (!credentialsResponse.ok) {
        await updateApplicationStatus(id, 'PAID', 'Нет доступных учетных данных для входа в визовый центр');
        return;
      }
      
      const { credential: activeCredentials } = await credentialsResponse.json();
      
      // Debug log to check credential structure
      console.log('Active credentials:', activeCredentials);
      
      const response = await fetch(`/api/applications/${id}`, {
        method: 'POST',
      });

      if (response.ok) {
        const data = await response.json();
        
        try {
          // Convert application data to bot format using utility function
          botPayload = convertApplicationToBotPayload(
            id,
            data.applicationData,
            {
              email: activeCredentials.email,
              password: activeCredentials.password
            }
          );

          // Validate the payload before sending
          const validation = validateBotPayload(botPayload);
          if (!validation.isValid) {
            console.error('Invalid bot payload:', validation.errors);
            await updateApplicationStatus(id, 'PAID', `Ошибка валидации: ${validation.errors.join(', ')}`);
            return;
          }

          const botResponse = await fetch('http://localhost:5000/api/submit-application', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(botPayload),
          });

          if (!botResponse.ok) {
            const errorData = await botResponse.json().catch(() => ({ error: 'Unknown error' }));
            console.error('Bot API error:', errorData);
            await updateApplicationStatus(id, 'PAID', `Ошибка бота: ${errorData.error || 'Не удалось отправить заявку'}`);
            return;
          }

          const botResult = await botResponse.json();
          console.log('Bot API response:', botResult);
        } catch (botError) {
          console.error('Failed to connect to bot API:', botError);
          await updateApplicationStatus(id, 'PAID', 'Не удалось подключиться к боту. Убедитесь, что бот запущен на http://localhost:5000');
          return;
        }
        
        // Log the data being sent to help debug
        const submissionData = {
          applicationId: id,
          credentialId: activeCredentials.id,
          notes: `Submitted using ${activeCredentials.email}`
        };
        
        console.log('Sending submission data:', submissionData);
        
        const submissionResponse = await fetch('/api/submissions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(submissionData),
        });

        if (!submissionResponse.ok) {
          const errorData = await submissionResponse.json().catch(() => ({ error: 'Unknown error' }));
          console.error('Failed to create submission record:', errorData);
          console.error('Response status:', submissionResponse.status);
          // Continue anyway - the bot submission was successful
        } else {
          // Increment application count for the credential
          try {
            await fetch(`/api/credentials/${activeCredentials.id}`, {
              method: 'PATCH',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                applicationCount: activeCredentials.applicationCount + 1
              }),
            });
          } catch (error) {
            console.error('Failed to update application count:', error);
          }
        }
        
        console.log(`=== ПОДАЧА ЗАЯВКИ ===
ID заявки: ${id}
ID учетной записи: ${activeCredentials.id}
Приоритет: ${activeCredentials.priority}
Использовано слотов: ${activeCredentials.applicationCount + 1}/${activeCredentials.maxApplications}

Для входа использовать:
Email: ${activeCredentials.email}
Password: ${activeCredentials.password}
${activeCredentials.description ? `Описание: ${activeCredentials.description}` : ''}

=== PAYLOAD ДЛЯ БОТА ===`, botPayload);

        console.log(`=== ОРИГИНАЛЬНЫЕ ДАННЫЕ ЗАЯВКИ ===`, data.applicationData);
        
        alert(`Заявка подана!

Для входа в визовый центр использовать:
Email: ${activeCredentials.email}
Password: ${activeCredentials.password}
Приоритет: ${activeCredentials.priority}
Использовано слотов: ${activeCredentials.applicationCount + 1}/${activeCredentials.maxApplications}
${activeCredentials.description ? `\nОписание: ${activeCredentials.description}` : ''}

Заявка отправлена в бот для автоматической подачи.
Связь заявки с учетной записью сохранена.
Данные заявки выведены в консоль.`);
        
        await updateApplicationStatus(id, 'COMPLETED');
      }
    } catch (error) {
      console.error('Error submitting application:', error);
      await updateApplicationStatus(id, 'PAID', 'Ошибка при подаче заявки');
    } finally {
      setProcessingId(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'NEW':
        return 'bg-blue-500';
      case 'PAID':
        return 'bg-emerald-500';
      case 'COMPLETED':
        return 'bg-gray-400';
      default:
        return 'bg-gray-400';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'NEW':
        return 'Новая';
      case 'PAID':
        return 'Оплаченная';
      case 'COMPLETED':
        return 'Завершенная';
      default:
        return status;
    }
  };


  // Filter and sort applications
  const filteredApplications = applications
    .filter(app => {
      const matchesSearch = 
        app.fullName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        app.passportNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
        app.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        app.phoneNumber.includes(searchQuery);
      
      const matchesStatus = filterStatus === 'ALL' || app.status === filterStatus;
      
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

  // Test applications functions
  const fetchTestAppsStatus = async () => {
    try {
      setTestAppsStatus(prev => ({ ...prev, loading: true }));
      const response = await fetch('/api/admin/create-test-applications');
      if (response.ok) {
        const data = await response.json();
        setTestAppsStatus({
          existing: data.existing,
          needed: data.needed,
          month: data.month,
          canCreate: data.canCreate,
          loading: false
        });
      }
    } catch (error) {
      console.error('Error fetching test apps status:', error);
      setTestAppsStatus(prev => ({ ...prev, loading: false }));
    }
  };

  const createTestApplications = async () => {
    if (creatingTestApps) return;
    
    setCreatingTestApps(true);
    try {
      const response = await fetch('/api/admin/create-test-applications', {
        method: 'POST'
      });
      
      const result = await response.json();
      
      if (response.ok) {
        alert(result.message);
        // Refresh applications list and test apps status
        await fetchApplications();
        await fetchTestAppsStatus();
      } else {
        alert(result.error || 'Ошибка при создании тестовых записей');
      }
    } catch (error) {
      console.error('Error creating test applications:', error);
      alert('Ошибка при создании тестовых записей');
    } finally {
      setCreatingTestApps(false);
    }
  };

  // Fetch test apps status on component mount
  useEffect(() => {
    if (session?.user) {
      fetchTestAppsStatus();
    }
  }, [session]);

  // Statistics
  const stats = {
    total: applications.length,
    new: applications.filter(app => app.status === 'NEW').length,
    paid: applications.filter(app => app.status === 'PAID').length,
    completed: applications.filter(app => app.status === 'COMPLETED').length,
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 mb-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
          <p className="text-gray-600">Загрузка панели управления...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                Визовый центр
              </h1>
              <span className="ml-3 px-3 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                Админ панель
              </span>
            </div>
            
            <nav className="flex items-center space-x-4">
              <a
                href="/admin/credentials"
                className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                </svg>
                Учетные данные
              </a>
              <button
                onClick={() => {
                  fetch('/api/auth/signout', { method: 'POST' })
                    .then(() => router.push('/auth/signin'));
                }}
                className="flex items-center px-4 py-2 text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                Выйти
              </button>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Statistics Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Всего заявок</p>
                <p className="text-3xl font-bold text-gray-900 mt-1">{stats.total}</p>
              </div>
              <div className="p-3 bg-gray-100 rounded-lg">
                <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Новые</p>
                <p className="text-3xl font-bold text-blue-600 mt-1">{stats.new}</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-lg">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Оплаченные</p>
                <p className="text-3xl font-bold text-emerald-600 mt-1">{stats.paid}</p>
              </div>
              <div className="p-3 bg-emerald-100 rounded-lg">
                <svg className="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Завершенные</p>
                <p className="text-3xl font-bold text-gray-600 mt-1">{stats.completed}</p>
              </div>
              <div className="p-3 bg-gray-100 rounded-lg">
                <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </div>

        </div>

        {/* Test Applications Control */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Тестовые записи для бронирования
              </h3>
              <p className="text-sm text-gray-600">
                {testAppsStatus.loading 
                  ? 'Загрузка...' 
                  : `${testAppsStatus.month}: ${testAppsStatus.existing}/10 записей создано`
                }
              </p>
            </div>
            <button
              onClick={createTestApplications}
              disabled={creatingTestApps || !testAppsStatus.canCreate || testAppsStatus.loading}
              className={`px-6 py-3 rounded-lg font-medium transition-colors flex items-center space-x-2 ${
                testAppsStatus.canCreate && !creatingTestApps && !testAppsStatus.loading
                  ? 'bg-blue-600 hover:bg-blue-700 text-white'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              {creatingTestApps ? (
                <>
                  <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Создаем...</span>
                </>
              ) : (
                <>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <span>
                    {testAppsStatus.canCreate 
                      ? `Создать ${testAppsStatus.needed} записей` 
                      : 'Записи уже созданы'
                    }
                  </span>
                </>
              )}
            </button>
          </div>
          {testAppsStatus.existing > 0 && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                💡 Тестовые записи помогают забронировать места заранее. 
                Они выглядят как обычные заявки с пометкой &quot;Системная тестовая запись&quot;.
              </p>
            </div>
          )}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Applications List */}
          <div className="lg:col-span-3">
            {/* Filters and Search */}
            <div className="bg-white rounded-xl shadow-sm p-6 mb-6 border border-gray-200">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="flex-1 max-w-md">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Поиск по имени, паспорту, email или телефону..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <svg className="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value as FilterStatus)}
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="ALL">Все статусы</option>
                    <option value="NEW">Новые</option>
                    <option value="PAID">Оплаченные</option>
                    <option value="COMPLETED">Завершенные</option>
                  </select>

                </div>
              </div>
            </div>

            {/* Applications Cards */}
            <div className="space-y-4">
              {filteredApplications.map((app) => (
                <div
                  key={app.id}
                  className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => openApplicationDetails(app)}
                >
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">{app.fullName}</h3>
                          {app.isFamily && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                              👨‍👩‍👧‍👦 Семья ({app.familySize})
                            </span>
                          )}
                          <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium text-white ${getStatusColor(app.status)}`}>
                            {getStatusLabel(app.status)}
                          </span>
                        </div>
                        
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm text-gray-600">
                          <div className="flex items-center">
                            <svg className="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
                            </svg>
                            {app.passportNumber}
                          </div>
                          <div className="flex items-center">
                            <svg className="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                            </svg>
                            {app.phoneNumber}
                          </div>
                          <div className="flex items-center">
                            <svg className="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            {app.email}
                          </div>
                          <div className="flex items-center">
                            <svg className="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            {new Date(app.createdAt).toLocaleDateString('ru-RU')}
                          </div>
                        </div>

                        <div className="mt-3 space-y-1">
                          <div className="flex items-center gap-4 text-sm">
                            <span className="text-gray-500">
                              Виза: <span className="text-gray-700 font-medium">{app.visaCategory} / {app.visaSubcategory}</span>
                            </span>
                            {app.vfsVisaCategoryCode && (
                              <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800">
                                VFS: {app.vfsVisaCategoryCode}/{app.vfsVisaSubcategoryCode}
                              </span>
                            )}
                            {app.vfsCenterCode && (
                              <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800">
                                📍 {app.vfsCenterCode}
                              </span>
                            )}
                            {app.amount && (
                              <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-yellow-100 text-yellow-800">
                                💰 {app.amount} {app.currencyCode}
                              </span>
                            )}
                          </div>
                          <div className="flex items-center gap-4 text-sm">
                            <span className="text-gray-500">
                              Центры: <span className="text-gray-700 font-medium">
                                {(() => {
                                  const centers = safeJsonParse(app.preferredCenters, []);
                                  return Array.isArray(centers) ? centers.join(', ') : 'Не указано';
                                })()}
                              </span>
                            </span>
                          </div>
                          
                          {/* Family Members Display */}
                          {app.isFamily && app.applicants && (
                            <div className="text-sm text-gray-600">
                              <span className="text-gray-500">Заявители: </span>
                              <span className="text-gray-700">
                                {(() => {
                                  const applicants = safeJsonParse(app.applicants, []);
                                  return Array.isArray(applicants) 
                                    ? applicants.map((applicant: any, index: number) => (
                                        <span key={index}>
                                          {applicant.firstName} {applicant.lastName}
                                          {applicant.relationshipToMain === 'spouse' && ' (супруг/а)'}
                                          {applicant.relationshipToMain === 'child' && ' (ребенок)'}
                                          {index < applicants.length - 1 && ', '}
                                        </span>
                                      ))
                                    : 'Ошибка отображения';
                                })()}
                              </span>
                            </div>
                          )}
                        </div>

                        {app.error && (
                          <div className="mt-3 flex items-start gap-2 p-3 bg-red-50 rounded-lg">
                            <svg className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <p className="text-sm text-red-700">{app.error}</p>
                          </div>
                        )}

                        {/* Bot Status Checker for COMPLETED applications */}
                        {app.status === 'COMPLETED' && (
                          <div className="mt-3">
                            <ApplicationStatusChecker applicationId={app.id} />
                          </div>
                        )}
                      </div>

                      {/* Action Buttons */}
                      <div className="flex-shrink-0 ml-4" onClick={(e) => e.stopPropagation()}>
                        {app.status === 'NEW' && (
                          <button
                            onClick={() => updateApplicationStatus(app.id, 'PAID')}
                            disabled={processingId === app.id}
                            className="flex items-center px-4 py-2 bg-emerald-600 text-white text-sm font-medium rounded-lg hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                          >
                            {processingId === app.id ? (
                              <>
                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Обработка...
                              </>
                            ) : (
                              <>
                                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                                Оплачено
                              </>
                            )}
                          </button>
                        )}
                        
                        {app.status === 'PAID' && (
                          <button
                            onClick={() => submitApplication(app.id)}
                            disabled={processingId === app.id}
                            className="flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                          >
                            {processingId === app.id ? (
                              <>
                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Отправка...
                              </>
                            ) : (
                              <>
                                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                                </svg>
                                Подать заявку
                              </>
                            )}
                          </button>
                        )}
                        
                        {app.status === 'COMPLETED' && (
                          <button
                            onClick={() => cancelApplication(app.id)}
                            disabled={processingId === app.id}
                            className="flex items-center px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                          >
                            {processingId === app.id ? (
                              <>
                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Отмена...
                              </>
                            ) : (
                              <>
                                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                                Отменить
                              </>
                            )}
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {filteredApplications.length === 0 && (
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12">
                  <div className="text-center">
                    <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 className="mt-4 text-lg font-medium text-gray-900">Заявки не найдены</h3>
                    <p className="mt-2 text-sm text-gray-500">
                      {searchQuery || filterStatus !== 'ALL' 
                        ? 'Попробуйте изменить параметры поиска или фильтры'
                        : 'Пока нет поданных заявок'}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-24 space-y-6">
              <BotStatus />
              
              {/* Bulk Status Checker */}
              <BulkStatusChecker 
                applicationIds={applications
                  .filter(app => app.status === 'COMPLETED')
                  .map(app => app.id)
                }
              />
              
              {/* Quick Stats */}
              <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Быстрая статистика</h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Конверсия</span>
                    <span className="text-sm font-semibold text-gray-900">
                      {stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${stats.total > 0 ? (stats.completed / stats.total) * 100 : 0}%` }}
                    ></div>
                  </div>
                  
                  <div className="pt-3 space-y-2">
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-600">Ожидают оплаты</span>
                      <span className="font-medium text-blue-600">{stats.new}</span>
                    </div>
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-600">Готовы к подаче</span>
                      <span className="font-medium text-emerald-600">{stats.paid}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <ApplicationModalV2
        application={selectedApplication}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  );
}