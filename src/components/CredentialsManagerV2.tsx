"use client"

import { useState, useEffect } from 'react';

interface VisaCenterCredential {
  id: string;
  email: string;
  password: string;
  isActive: boolean;
  description?: string;
  priority: string;
  applicationCount: number;
  maxApplications: number;
  createdAt: string;
  updatedAt: string;
}

export default function CredentialsManagerV2() {
  const [credentials, setCredentials] = useState<VisaCenterCredential[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingId, setProcessingId] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    description: '',
    isActive: true,
    priority: 'standard',
    maxApplications: 12
  });

  useEffect(() => {
    fetchCredentials();
  }, []);

  const fetchCredentials = async () => {
    try {
      const response = await fetch('/api/credentials');
      if (response.ok) {
        const data = await response.json();
        setCredentials(data);
      }
    } catch (error) {
      console.error('Error fetching credentials:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const url = editingId ? `/api/credentials/${editingId}` : '/api/credentials';
    const method = editingId ? 'PATCH' : 'POST';
    
    try {
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        await fetchCredentials();
        resetForm();
      } else {
        const error = await response.json();
        alert(error.error || 'Произошла ошибка');
      }
    } catch (error) {
      console.error('Error saving credential:', error);
      alert('Произошла ошибка при сохранении');
    }
  };

  const handleEdit = (credential: VisaCenterCredential) => {
    setFormData({
      email: credential.email,
      password: credential.password,
      description: credential.description || '',
      isActive: credential.isActive,
      priority: credential.priority,
      maxApplications: credential.maxApplications
    });
    setEditingId(credential.id);
    setShowAddForm(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Вы уверены, что хотите удалить эту учетную запись?')) {
      return;
    }

    setProcessingId(id);
    try {
      const response = await fetch(`/api/credentials/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        await fetchCredentials();
      } else {
        const error = await response.json();
        alert(error.error || 'Произошла ошибка при удалении');
      }
    } catch (error) {
      console.error('Error deleting credential:', error);
      alert('Произошла ошибка при удалении');
    } finally {
      setProcessingId(null);
    }
  };

  const toggleActive = async (id: string, isActive: boolean) => {
    setProcessingId(id);
    try {
      const response = await fetch(`/api/credentials/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive: !isActive }),
      });

      if (response.ok) {
        await fetchCredentials();
      }
    } catch (error) {
      console.error('Error toggling credential:', error);
    } finally {
      setProcessingId(null);
    }
  };

  const resetApplicationCount = async (id: string) => {
    if (!confirm('Сбросить счетчик заявок для этой учетной записи?')) {
      return;
    }

    setProcessingId(id);
    try {
      const response = await fetch(`/api/credentials/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ applicationCount: 0 }),
      });

      if (response.ok) {
        await fetchCredentials();
      }
    } catch (error) {
      console.error('Error resetting count:', error);
    } finally {
      setProcessingId(null);
    }
  };

  const resetForm = () => {
    setFormData({
      email: '',
      password: '',
      description: '',
      isActive: true,
      priority: 'standard',
      maxApplications: 12
    });
    setShowAddForm(false);
    setEditingId(null);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'standard':
        return 'bg-blue-100 text-blue-800';
      case 'prime':
        return 'bg-purple-100 text-purple-800';
      case 'premium':
        return 'bg-gold-100 text-yellow-800 bg-gradient-to-r from-yellow-100 to-orange-100';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'standard':
        return '🥉';
      case 'prime':
        return '🥈';
      case 'premium':
        return '🥇';
      default:
        return '📋';
    }
  };

  const getUsagePercentage = (count: number, max: number) => {
    return Math.round((count / max) * 100);
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 70) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 mb-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
          <p className="text-gray-600">Загрузка учетных записей...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">Учетные записи</h1>
              <span className="ml-3 px-3 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                {credentials.length} записей
              </span>
            </div>
            
            <div className="flex items-center gap-4">
              <a
                href="/admin"
                className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Назад к заявкам
              </a>
              <button
                onClick={() => setShowAddForm(true)}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                Добавить запись
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Всего записей</p>
                <p className="text-3xl font-bold text-gray-900 mt-1">{credentials.length}</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-lg">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Активных</p>
                <p className="text-3xl font-bold text-green-600 mt-1">
                  {credentials.filter(c => c.isActive).length}
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-lg">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Заявок обработано</p>
                <p className="text-3xl font-bold text-purple-600 mt-1">
                  {credentials.reduce((sum, c) => sum + c.applicationCount, 0)}
                </p>
              </div>
              <div className="p-3 bg-purple-100 rounded-lg">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Средняя загрузка</p>
                <p className="text-3xl font-bold text-orange-600 mt-1">
                  {credentials.length > 0 ? Math.round(
                    credentials.reduce((sum, c) => sum + getUsagePercentage(c.applicationCount, c.maxApplications), 0) / credentials.length
                  ) : 0}%
                </p>
              </div>
              <div className="p-3 bg-orange-100 rounded-lg">
                <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        {/* Credentials List */}
        <div className="space-y-4">
          {credentials
            .sort((a, b) => {
              // Sort by priority first, then by usage
              const priorityOrder = { 'standard': 0, 'prime': 1, 'premium': 2 };
              const aPriority = priorityOrder[a.priority as keyof typeof priorityOrder] || 0;
              const bPriority = priorityOrder[b.priority as keyof typeof priorityOrder] || 0;
              
              if (aPriority !== bPriority) return aPriority - bPriority;
              return a.applicationCount - b.applicationCount;
            })
            .map((credential) => {
              const usagePercentage = getUsagePercentage(credential.applicationCount, credential.maxApplications);
              
              return (
                <div key={credential.id} className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">{credential.email}</h3>
                          
                          <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getPriorityColor(credential.priority)}`}>
                            <span className="mr-1">{getPriorityIcon(credential.priority)}</span>
                            {credential.priority.charAt(0).toUpperCase() + credential.priority.slice(1)}
                          </span>
                          
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            credential.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {credential.isActive ? '🟢 Активна' : '🔴 Неактивна'}
                          </span>
                        </div>

                        {credential.description && (
                          <p className="text-sm text-gray-600 mb-3">{credential.description}</p>
                        )}

                        {/* Usage Progress Bar */}
                        <div className="mb-3">
                          <div className="flex justify-between items-center mb-1">
                            <span className="text-sm font-medium text-gray-700">
                              Использование: {credential.applicationCount}/{credential.maxApplications}
                            </span>
                            <span className="text-sm text-gray-500">{usagePercentage}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className={`h-2 rounded-full transition-all duration-300 ${getUsageColor(usagePercentage)}`}
                              style={{ width: `${usagePercentage}%` }}
                            ></div>
                          </div>
                        </div>

                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span>Создана: {new Date(credential.createdAt).toLocaleDateString('ru-RU')}</span>
                          <span>Обновлена: {new Date(credential.updatedAt).toLocaleDateString('ru-RU')}</span>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center gap-2 ml-4">
                        <button
                          onClick={() => toggleActive(credential.id, credential.isActive)}
                          disabled={processingId === credential.id}
                          className={`px-3 py-1 text-xs font-medium rounded-lg transition-colors ${
                            credential.isActive 
                              ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                              : 'bg-green-100 text-green-700 hover:bg-green-200'
                          } disabled:opacity-50`}
                        >
                          {credential.isActive ? 'Деактивировать' : 'Активировать'}
                        </button>

                        <button
                          onClick={() => resetApplicationCount(credential.id)}
                          disabled={processingId === credential.id || credential.applicationCount === 0}
                          className="px-3 py-1 text-xs font-medium bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 disabled:opacity-50 transition-colors"
                        >
                          Сбросить счетчик
                        </button>

                        <button
                          onClick={() => handleEdit(credential)}
                          className="px-3 py-1 text-xs font-medium bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                        >
                          Изменить
                        </button>

                        <button
                          onClick={() => handleDelete(credential.id)}
                          disabled={processingId === credential.id}
                          className="px-3 py-1 text-xs font-medium bg-red-100 text-red-700 rounded-lg hover:bg-red-200 disabled:opacity-50 transition-colors"
                        >
                          Удалить
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}

          {credentials.length === 0 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12">
              <div className="text-center">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                </svg>
                <h3 className="mt-4 text-lg font-medium text-gray-900">Учетные записи не найдены</h3>
                <p className="mt-2 text-sm text-gray-500">
                  Добавьте первую учетную запись для работы с визовым центром
                </p>
                <button
                  onClick={() => setShowAddForm(true)}
                  className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Добавить учетную запись
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Add/Edit Form Modal */}
      {showAddForm && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onClick={resetForm}></div>

            <div className="inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <form onSubmit={handleSubmit}>
                <div className="bg-white px-6 py-6">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-semibold text-gray-900">
                      {editingId ? 'Редактировать запись' : 'Добавить учетную запись'}
                    </h3>
                    <button
                      type="button"
                      onClick={resetForm}
                      className="text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                      <input
                        type="email"
                        required
                        value={formData.email}
                        onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Пароль *</label>
                      <input
                        type="password"
                        required
                        value={formData.password}
                        onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="••••••••"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Описание</label>
                      <input
                        type="text"
                        value={formData.description}
                        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Описание учетной записи"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Приоритет</label>
                      <select
                        value={formData.priority}
                        onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="standard">🥉 Standard</option>
                        <option value="prime">🥈 Prime</option>
                        <option value="premium">🥇 Premium</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Максимум заявок</label>
                      <input
                        type="number"
                        min="1"
                        max="50"
                        value={formData.maxApplications}
                        onChange={(e) => setFormData(prev => ({ ...prev, maxApplications: parseInt(e.target.value) }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="isActive"
                        checked={formData.isActive}
                        onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
                        Активная учетная запись
                      </label>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 px-6 py-4 flex items-center justify-end gap-3">
                  <button
                    type="button"
                    onClick={resetForm}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Отмена
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    {editingId ? 'Сохранить' : 'Добавить'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}