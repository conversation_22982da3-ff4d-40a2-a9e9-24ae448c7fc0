"use client"

import { useState, useEffect } from 'react';

interface BotStatusResponse {
  status: string;
  message?: string;
  queue?: {
    pending: number;
    processing: number;
    completed: number;
    failed: number;
  };
  [key: string]: any;
}

interface HealthResponse {
  status: string;
  message?: string;
  [key: string]: any;
}


export default function BotStatus() {
  const [botStatus, setBotStatus] = useState<BotStatusResponse | null>(null);
  const [health, setHealth] = useState<HealthResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const checkBotStatus = async () => {
    try {
      setError(null);
      
      // Check bot status
      const statusResponse = await fetch('http://localhost:5000/api/bot-status');
      if (statusResponse.ok) {
        const statusData = await statusResponse.json();
        setBotStatus(statusData);
      } else {
        setBotStatus(null);
        setError('Не удалось получить статус бота');
      }

      // Check health
      const healthResponse = await fetch('http://localhost:5000/api/health');
      if (healthResponse.ok) {
        const healthData = await healthResponse.json();
        setHealth(healthData);
      } else {
        setHealth(null);
      }
    } catch (err) {
      console.error('Error checking bot status:', err);
      setError('Бот недоступен. Убедитесь, что он запущен на http://localhost:5000');
      setBotStatus(null);
      setHealth(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkBotStatus();
    
    // Check status every 30 seconds
    const interval = setInterval(checkBotStatus, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string | null) => {
    if (!status) return 'bg-red-500';
    
    const lowerStatus = status.toLowerCase();
    if (lowerStatus.includes('running') || lowerStatus.includes('ok') || lowerStatus.includes('healthy')) {
      return 'bg-green-500';
    } else if (lowerStatus.includes('warning') || lowerStatus.includes('busy')) {
      return 'bg-yellow-500';
    } else {
      return 'bg-red-500';
    }
  };

  const getStatusText = () => {
    if (error) return 'Недоступен';
    if (!botStatus) return 'Неизвестно';
    return botStatus.status || 'Неизвестно';
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-4 mb-4">
      <h2 className="text-lg font-semibold mb-3">Статус бота</h2>
      
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">Статус:</span>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${getStatusColor(botStatus?.status || null)} animate-pulse`}></div>
            <span className="text-sm">{getStatusText()}</span>
          </div>
        </div>

        {health && (
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Здоровье сервера:</span>
            <span className="text-sm">{health.status || 'OK'}</span>
          </div>
        )}

        {/* Queue Information */}
        {botStatus?.queue && (
          <div className="mt-3 space-y-2">
            <h4 className="text-sm font-semibold text-gray-700">Очередь бота:</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex justify-between">
                <span className="text-gray-600">Ожидают:</span>
                <span className="font-medium text-blue-600">{botStatus.queue.pending}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Обработка:</span>
                <span className="font-medium text-yellow-600">{botStatus.queue.processing}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Завершено:</span>
                <span className="font-medium text-green-600">{botStatus.queue.completed}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Ошибки:</span>
                <span className="font-medium text-red-600">{botStatus.queue.failed}</span>
              </div>
            </div>
          </div>
        )}

        {botStatus?.message && (
          <div className="mt-2 p-2 bg-gray-50 rounded text-sm text-gray-600">
            {botStatus.message}
          </div>
        )}

        {error && (
          <div className="mt-2 p-2 bg-red-50 rounded text-sm text-red-600">
            {error}
          </div>
        )}

        <div className="pt-2 border-t">
          <button
            onClick={checkBotStatus}
            disabled={loading}
            className="text-sm text-blue-600 hover:text-blue-800 disabled:text-gray-400"
          >
            {loading ? 'Проверка...' : 'Обновить статус'}
          </button>
        </div>
      </div>
    </div>
  );
}