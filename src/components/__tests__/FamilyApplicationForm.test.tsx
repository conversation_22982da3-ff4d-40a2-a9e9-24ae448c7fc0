import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import FamilyApplicationForm from '../FamilyApplicationForm'

// Setup fetch mock
Object.defineProperty(window, 'fetch', {
  writable: true,
  value: jest.fn(),
})

// Mock onToggleMode function
const mockOnToggleMode = jest.fn()

// Helper function to fill required fields for valid form
const fillValidForm = async (user: any) => {
  // Fill applicant fields
  await user.type(screen.getByLabelText(/Имя \*/), 'JOHN')
  await user.type(screen.getByLabelText(/Фамилия \*/), 'DOE')
  await user.type(screen.getByLabelText(/Дата рождения \*/), '1990-01-01')
  await user.type(screen.getByLabelText(/Номер паспорта \*/), 'N12345678')
  await user.type(screen.getByLabelText(/Срок действия паспорта \*/), '2030-01-01')
  
  // Fill contact info
  await user.type(screen.getByLabelText(/Номер телефона \*/), '+77001234567')
  await user.type(screen.getByLabelText(/Email \*/), '<EMAIL>')
  
  // Fill visa preferences
  await user.selectOptions(screen.getByLabelText(/Категория визы \*/), 'TOURISM')
  await user.selectOptions(screen.getByLabelText(/Цель поездки \*/), 'INDIVIDUAL')
  
  // Select at least one visa center
  await user.click(screen.getByLabelText('ALMATY'))
  
  // Select date flexibility
  await user.click(screen.getByLabelText(/Любая доступная дата/))
}

// Helper function to fill partially invalid form
const fillPartiallyInvalidForm = async (user: any) => {
  // Fill applicant fields (missing passport number)
  await user.type(screen.getByLabelText(/Имя \*/), 'JOHN')
  await user.type(screen.getByLabelText(/Фамилия \*/), 'DOE')
  await user.type(screen.getByLabelText(/Дата рождения \*/), '1990-01-01')
  // Skip passport number intentionally
  await user.type(screen.getByLabelText(/Срок действия паспорта \*/), '2030-01-01')
  
  // Fill contact info
  await user.type(screen.getByLabelText(/Номер телефона \*/), '+77001234567')
  await user.type(screen.getByLabelText(/Email \*/), '<EMAIL>')
  
  // Fill visa preferences
  await user.selectOptions(screen.getByLabelText(/Категория визы \*/), 'TOURISM')
  await user.selectOptions(screen.getByLabelText(/Цель поездки \*/), 'INDIVIDUAL')
  
  // Select at least one visa center
  await user.click(screen.getByLabelText('ALMATY'))
  
  // Select date flexibility
  await user.click(screen.getByLabelText(/Любая доступная дата/))
}

describe('FamilyApplicationForm', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Mock successful fetch response
    global.fetch = jest.fn().mockResolvedValue({
      ok: true,
      json: async () => ({ id: 'test-123' })
    })
  })

  describe('Submit button enable/disable logic', () => {
    it('should have submit button disabled initially (form is invalid)', () => {
      render(<FamilyApplicationForm onToggleMode={mockOnToggleMode} />)
      
      const submitButton = screen.getByRole('button', { name: /Подать семейную заявку/ })
      expect(submitButton).toBeDisabled()
    })

    it('should enable submit button when all required fields are filled correctly', async () => {
      const user = userEvent.setup()
      render(<FamilyApplicationForm onToggleMode={mockOnToggleMode} />)
      
      const submitButton = screen.getByRole('button', { name: /Подать семейную заявку/ })
      
      // Initially disabled
      expect(submitButton).toBeDisabled()
      
      // Fill valid form
      await fillValidForm(user)
      
      // Wait for form validation to complete
      await waitFor(() => {
        expect(submitButton).toBeEnabled()
      })
    })

    it('should keep submit button disabled when required fields are missing', async () => {
      const user = userEvent.setup()
      render(<FamilyApplicationForm onToggleMode={mockOnToggleMode} />)
      
      const submitButton = screen.getByRole('button', { name: /Подать семейную заявку/ })
      
      // Fill partially invalid form (missing passport number)
      await fillPartiallyInvalidForm(user)
      
      // Wait for form validation to complete
      await waitFor(() => {
        expect(submitButton).toBeDisabled()
      })
    })

    it('should disable submit button during form submission', async () => {
      const user = userEvent.setup()
      
      // Mock delayed fetch response
      global.fetch = jest.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          ok: true,
          json: async () => ({ id: 'test-123' })
        }), 100))
      )
      
      render(<FamilyApplicationForm onToggleMode={mockOnToggleMode} />)
      
      const submitButton = screen.getByRole('button', { name: /Подать семейную заявку/ })
      
      // Fill valid form
      await fillValidForm(user)
      
      // Wait for button to be enabled
      await waitFor(() => {
        expect(submitButton).toBeEnabled()
      })
      
      // Click submit
      await user.click(submitButton)
      
      // Button should be disabled during submission
      expect(submitButton).toBeDisabled()
      expect(submitButton).toHaveTextContent(/Отправка семейной заявки.../)
    })

    it('should re-enable submit button after successful submission', async () => {
      const user = userEvent.setup()
      render(<FamilyApplicationForm onToggleMode={mockOnToggleMode} />)
      
      const submitButton = screen.getByRole('button', { name: /Подать семейную заявку/ })
      
      // Fill valid form
      await fillValidForm(user)
      
      // Wait for button to be enabled
      await waitFor(() => {
        expect(submitButton).toBeEnabled()
      })
      
      // Click submit
      await user.click(submitButton)
      
      // Wait for submission to complete
      await waitFor(() => {
        expect(submitButton).toBeEnabled()
      })
    })

    it('should re-enable submit button after failed submission', async () => {
      const user = userEvent.setup()
      
      // Mock failed fetch response
      global.fetch = jest.fn().mockResolvedValue({
        ok: false,
        json: async () => ({ error: 'Submission failed' })
      })
      
      render(<FamilyApplicationForm onToggleMode={mockOnToggleMode} />)
      
      const submitButton = screen.getByRole('button', { name: /Подать семейную заявку/ })
      
      // Fill valid form
      await fillValidForm(user)
      
      // Wait for button to be enabled
      await waitFor(() => {
        expect(submitButton).toBeEnabled()
      })
      
      // Click submit
      await user.click(submitButton)
      
      // Wait for submission to complete
      await waitFor(() => {
        expect(submitButton).toBeEnabled()
      })
    })
  })

  describe('Form validation behavior', () => {
    it('should show validation errors for invalid fields', async () => {
      const user = userEvent.setup()
      render(<FamilyApplicationForm onToggleMode={mockOnToggleMode} />)
      
      // Fill invalid passport number
      await user.type(screen.getByLabelText(/Номер паспорта \*/), 'INVALID')
      await user.tab() // Trigger validation
      
      await waitFor(() => {
        expect(screen.getByText(/Формат: 1 буква \+ 8 цифр/)).toBeInTheDocument()
      })
      
      const submitButton = screen.getByRole('button', { name: /Подать семейную заявку/ })
      expect(submitButton).toBeDisabled()
    })

    it('should validate phone number format', async () => {
      const user = userEvent.setup()
      render(<FamilyApplicationForm onToggleMode={mockOnToggleMode} />)
      
      // Fill invalid phone number
      await user.type(screen.getByLabelText(/Номер телефона \*/), '*********')
      await user.tab() // Trigger validation
      
      await waitFor(() => {
        expect(screen.getByText(/Формат: \+7XXXXXXXXXX/)).toBeInTheDocument()
      })
      
      const submitButton = screen.getByRole('button', { name: /Подать семейную заявку/ })
      expect(submitButton).toBeDisabled()
    })

    it('should validate email format', async () => {
      const user = userEvent.setup()
      render(<FamilyApplicationForm onToggleMode={mockOnToggleMode} />)
      
      // Fill invalid email
      await user.type(screen.getByLabelText(/Email \*/), 'invalid-email')
      await user.tab() // Trigger validation
      
      await waitFor(() => {
        expect(screen.getByText(/Введите корректный email/)).toBeInTheDocument()
      })
      
      const submitButton = screen.getByRole('button', { name: /Подать семейную заявку/ })
      expect(submitButton).toBeDisabled()
    })
  })

  describe('Family members functionality', () => {
    it('should enable submit button when adding valid family members', async () => {
      const user = userEvent.setup()
      render(<FamilyApplicationForm onToggleMode={mockOnToggleMode} />)
      
      // Fill main applicant data
      await fillValidForm(user)
      
      // Add spouse
      await user.click(screen.getByText('+ Супруг/а'))
      
      // Fill spouse data
      const applicantInputs = screen.getAllByLabelText(/Имя \*/)
      const lastNameInputs = screen.getAllByLabelText(/Фамилия \*/)
      const dobInputs = screen.getAllByLabelText(/Дата рождения \*/)
      const passportInputs = screen.getAllByLabelText(/Номер паспорта \*/)
      const expiryInputs = screen.getAllByLabelText(/Срок действия паспорта \*/)
      
      await user.type(applicantInputs[1], 'JANE')
      await user.type(lastNameInputs[1], 'DOE')
      await user.type(dobInputs[1], '1992-01-01')
      await user.type(passportInputs[1], 'N87654321')
      await user.type(expiryInputs[1], '2030-01-01')
      
      // Wait for form validation to complete
      await waitFor(() => {
        const submitButton = screen.getByRole('button', { name: /Подать семейную заявку/ })
        expect(submitButton).toBeEnabled()
      })
    })

    it('should disable submit button if any family member has invalid data', async () => {
      const user = userEvent.setup()
      render(<FamilyApplicationForm onToggleMode={mockOnToggleMode} />)
      
      // Fill main applicant data
      await fillValidForm(user)
      
      // Add spouse
      await user.click(screen.getByText('+ Супруг/а'))
      
      // Fill spouse data with invalid passport
      const applicantInputs = screen.getAllByLabelText(/Имя \*/)
      const lastNameInputs = screen.getAllByLabelText(/Фамилия \*/)
      const dobInputs = screen.getAllByLabelText(/Дата рождения \*/)
      const passportInputs = screen.getAllByLabelText(/Номер паспорта \*/)
      const expiryInputs = screen.getAllByLabelText(/Срок действия паспорта \*/)
      
      await user.type(applicantInputs[1], 'JANE')
      await user.type(lastNameInputs[1], 'DOE')
      await user.type(dobInputs[1], '1992-01-01')
      await user.type(passportInputs[1], 'INVALID') // Invalid passport format
      await user.type(expiryInputs[1], '2030-01-01')
      
      // Wait for form validation to complete
      await waitFor(() => {
        const submitButton = screen.getByRole('button', { name: /Подать семейную заявку/ })
        expect(submitButton).toBeDisabled()
      })
    })
  })

  describe('Submit button logic consistency', () => {
    it('should only depend on isSubmitting and isValid states', async () => {
      const user = userEvent.setup()
      render(<FamilyApplicationForm onToggleMode={mockOnToggleMode} />)
      
      const submitButton = screen.getByRole('button', { name: /Подать семейную заявку/ })
      
      // Initial state: form is invalid, not submitting -> disabled
      expect(submitButton).toBeDisabled()
      
      // Fill valid form: form is valid, not submitting -> enabled
      await fillValidForm(user)
      await waitFor(() => {
        expect(submitButton).toBeEnabled()
      })
      
      // Make form invalid again by clearing required field
      const firstNameInput = screen.getByLabelText(/Имя \*/)
      await user.clear(firstNameInput)
      
      // Form is invalid, not submitting -> disabled
      await waitFor(() => {
        expect(submitButton).toBeDisabled()
      })
      
      // Restore valid state
      await user.type(firstNameInput, 'JOHN')
      await waitFor(() => {
        expect(submitButton).toBeEnabled()
      })
    })
  })
})
