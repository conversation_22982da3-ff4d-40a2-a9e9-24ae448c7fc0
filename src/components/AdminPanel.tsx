"use client"

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import ApplicationModal from './ApplicationModal';
import BotStatus from './BotStatus';

interface VisaApplication {
  id: string;
  fullName: string;
  dateOfBirth: string;
  gender: string;
  passportNumber: string;
  passportExpiry: string;
  nationality: string;
  phoneNumber: string;
  email: string;
  visaCategory: string;
  visaSubcategory: string;
  preferredCenters: string[];
  dateFlexibility: string;
  preferredDateRange?: any;
  urgency: string;
  alternativeOptions?: string[];
  comments?: string;
  status: 'NEW' | 'PAID' | 'COMPLETED';
  error: string | null;
  createdAt: string;
  updatedAt: string;
}

export default function AdminPanel() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [applications, setApplications] = useState<VisaApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingId, setProcessingId] = useState<string | null>(null);
  const [selectedApplication, setSelectedApplication] = useState<VisaApplication | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/signin');
      return;
    }

    fetchApplications();
  }, [session, status, router]);

  const fetchApplications = async () => {
    try {
      const response = await fetch('/api/applications');
      if (response.ok) {
        const data = await response.json();
        setApplications(data);
      }
    } catch (error) {
      console.error('Error fetching applications:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateApplicationStatus = async (id: string, newStatus: 'NEW' | 'PAID' | 'COMPLETED', error?: string) => {
    setProcessingId(id);
    try {
      const response = await fetch(`/api/applications/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus, error }),
      });

      if (response.ok) {
        await fetchApplications();
      }
    } catch (error) {
      console.error('Error updating application:', error);
    } finally {
      setProcessingId(null);
    }
  };

  const openApplicationDetails = (app: VisaApplication) => {
    setSelectedApplication(app);
    setIsModalOpen(true);
  };

  const cancelApplication = async (id: string) => {
    setProcessingId(id);
    try {
      // Get submission record to find which credential was used
      const submissionResponse = await fetch(`/api/submissions/${id}`);
      
      if (!submissionResponse.ok) {
        await updateApplicationStatus(id, 'NEW', 'Не найдена информация о подаче заявки');
        return;
      }
      
      const submission = await submissionResponse.json();
      
      // Update submission status to cancelled
      await fetch(`/api/submissions/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'CANCELLED',
          notes: `Cancelled at ${new Date().toISOString()}`
        }),
      });
      
      // Prepare cancellation data with fullName
      const cancellationData = {
        fullName: submission.application.fullName
      };
      
      // Log cancellation info with credentials and JSON data
      console.log(`=== ОТМЕНА ЗАЯВКИ ===
ID заявки: ${id}
ID учетной записи: ${submission.credential.id}

Для отмены использовать:
Email: ${submission.credential.email}
Password: ${submission.credential.password}
${submission.credential.description ? `Описание: ${submission.credential.description}` : ''}

JSON данные для отмены:`, JSON.stringify(cancellationData, null, 2), `

Заявка была подана: ${new Date(submission.submittedAt).toLocaleString('ru-RU')}`);
      
      // Show alert with credentials and JSON
      alert(`Заявка отменена!

Для отмены в визовом центре использовать:
Email: ${submission.credential.email}
Password: ${submission.credential.password}
${submission.credential.description ? `\nОписание: ${submission.credential.description}` : ''}

JSON данные: ${JSON.stringify(cancellationData)}

Заявка была подана: ${new Date(submission.submittedAt).toLocaleString('ru-RU')}
Подробности в консоли.`);
      
      // Update status back to new
      await updateApplicationStatus(id, 'NEW');
      
    } catch (error) {
      console.error('Error cancelling application:', error);
      await updateApplicationStatus(id, 'NEW', 'Ошибка при отмене заявки');
    } finally {
      setProcessingId(null);
    }
  };

  const submitApplication = async (id: string) => {
    setProcessingId(id);
    try {
      // Get active credentials first
      const credentialsResponse = await fetch('/api/credentials/active');
      
      if (!credentialsResponse.ok) {
        await updateApplicationStatus(id, 'PAID', 'Нет активных учетных данных для входа в визовый центр');
        return;
      }
      
      const activeCredentials = await credentialsResponse.json();
      
      // Get application data
      const response = await fetch(`/api/applications/${id}`, {
        method: 'POST',
      });

      if (response.ok) {
        const data = await response.json();
        
        // Send application to bot API
        try {
          const botResponse = await fetch('http://localhost:5000/api/submit-application', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              application_id: id,
              email: activeCredentials.email,
              password: activeCredentials.password,
              applicationData: data.applicationData
            }),
          });

          if (!botResponse.ok) {
            const errorData = await botResponse.json().catch(() => ({ error: 'Unknown error' }));
            console.error('Bot API error:', errorData);
            await updateApplicationStatus(id, 'PAID', `Ошибка бота: ${errorData.error || 'Не удалось отправить заявку'}`);
            return;
          }

          const botResult = await botResponse.json();
          console.log('Bot API response:', botResult);
        } catch (botError) {
          console.error('Failed to connect to bot API:', botError);
          await updateApplicationStatus(id, 'PAID', 'Не удалось подключиться к боту. Убедитесь, что бот запущен на http://localhost:5000');
          return;
        }
        
        // Create submission record linking application to credential
        const submissionResponse = await fetch('/api/submissions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            applicationId: id,
            credentialId: activeCredentials.id,
            notes: `Submitted using ${activeCredentials.email}`
          }),
        });

        if (!submissionResponse.ok) {
          console.error('Failed to create submission record');
        }
        
        // Log submission info with credentials
        console.log(`=== ПОДАЧА ЗАЯВКИ ===
ID заявки: ${id}
ID учетной записи: ${activeCredentials.id}

Для входа использовать:
Email: ${activeCredentials.email}
Password: ${activeCredentials.password}
${activeCredentials.description ? `Описание: ${activeCredentials.description}` : ''}

=== ДАННЫЕ ЗАЯВКИ ===`, data.applicationData);
        
        // Show alert with credentials
        alert(`Заявка подана!

Для входа в визовый центр использовать:
Email: ${activeCredentials.email}
Password: ${activeCredentials.password}
${activeCredentials.description ? `\nОписание: ${activeCredentials.description}` : ''}

Заявка отправлена в бот для автоматической подачи.
Связь заявки с учетной записью сохранена.
Данные заявки выведены в консоль.`);
        
        // Update status to completed
        await updateApplicationStatus(id, 'COMPLETED');
      }
    } catch (error) {
      console.error('Error submitting application:', error);
      await updateApplicationStatus(id, 'PAID', 'Ошибка при подаче заявки');
    } finally {
      setProcessingId(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'NEW':
        return 'bg-blue-100 text-blue-800';
      case 'PAID':
        return 'bg-green-100 text-green-800';
      case 'COMPLETED':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'NEW':
        return 'Новая';
      case 'PAID':
        return 'Оплаченная';
      case 'COMPLETED':
        return 'Завершенная';
      default:
        return status;
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'very_urgent':
        return 'bg-red-100 text-red-800';
      case 'urgent':
        return 'bg-orange-100 text-orange-800';
      case 'normal':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getUrgencyLabel = (urgency: string) => {
    switch (urgency) {
      case 'very_urgent':
        return 'Очень срочная';
      case 'urgent':
        return 'Срочная';
      case 'normal':
        return 'Обычная';
      default:
        return urgency;
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">Загрузка...</div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-6">
          <div className="lg:col-span-3">
            <div className="bg-white shadow-sm rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex justify-between items-center">
                  <h1 className="text-2xl font-semibold text-gray-900">
                    Панель администратора - Заявки на визу
                  </h1>
                  <a
                    href="/admin/credentials"
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    Управление учетными данными
                  </a>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  Всего заявок: {applications.length}
                </p>
              </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Заявитель
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Контакты
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Виза
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Срочность
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Статус
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ошибка
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Дата создания
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Действия
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {applications.map((app) => (
                  <tr key={app.id} className="hover:bg-gray-50 cursor-pointer" onClick={() => openApplicationDetails(app)}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {app.fullName}
                      </div>
                      <div className="text-sm text-gray-500">
                        {app.passportNumber}
                      </div>
                      <div className="text-sm text-gray-500">
                        {new Date(app.dateOfBirth).toLocaleDateString('ru-RU')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {app.phoneNumber}
                      </div>
                      <div className="text-sm text-gray-500">
                        {app.email}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {app.visaCategory}
                      </div>
                      <div className="text-sm text-gray-500">
                        {app.visaSubcategory}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getUrgencyColor(app.urgency)}`}>
                        {getUrgencyLabel(app.urgency)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(app.status)}`}>
                        {getStatusLabel(app.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {app.error && (
                        <div className="text-sm text-red-600 max-w-xs truncate" title={app.error}>
                          {app.error}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(app.createdAt).toLocaleDateString('ru-RU')} {new Date(app.createdAt).toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' })}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-y-1" onClick={(e) => e.stopPropagation()}>
                      {app.status === 'NEW' && (
                        <button
                          onClick={() => updateApplicationStatus(app.id, 'PAID')}
                          disabled={processingId === app.id}
                          className="block w-full text-center px-3 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700 disabled:opacity-50"
                        >
                          {processingId === app.id ? 'Обработка...' : 'Оплачено'}
                        </button>
                      )}
                      
                      {app.status === 'PAID' && (
                        <button
                          onClick={() => submitApplication(app.id)}
                          disabled={processingId === app.id}
                          className="block w-full text-center px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 disabled:opacity-50"
                        >
                          {processingId === app.id ? 'Отправка...' : 'Подать заявку'}
                        </button>
                      )}
                      
                      {app.status === 'COMPLETED' && (
                        <button
                          onClick={() => cancelApplication(app.id)}
                          disabled={processingId === app.id}
                          className="block w-full text-center px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700 disabled:opacity-50"
                        >
                          {processingId === app.id ? 'Отмена...' : 'Отменить заявку'}
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

              {applications.length === 0 && (
                <div className="text-center py-12">
                  <div className="text-gray-500">Пока нет поданных заявок</div>
                </div>
              )}
            </div>
          </div>
          <div className="lg:col-span-1">
            <BotStatus />
          </div>
        </div>
      </div>
      
      <ApplicationModal
        application={selectedApplication}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  );
}