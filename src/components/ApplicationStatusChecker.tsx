"use client"

import { useState } from 'react';

interface ApplicationStatus {
  application_id: string;
  status: string;
  message?: string;
  progress?: string;
  timestamp?: string;
  [key: string]: any;
}

interface ApplicationStatusCheckerProps {
  applicationId: string;
  onStatusUpdate?: (status: ApplicationStatus) => void;
}

export default function ApplicationStatusChecker({ applicationId, onStatusUpdate }: ApplicationStatusCheckerProps) {
  const [status, setStatus] = useState<ApplicationStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkStatus = async () => {
    if (!applicationId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`http://localhost:5000/api/application-status/${applicationId}`);
      
      if (response.ok) {
        const statusData = await response.json() as ApplicationStatus;
        setStatus(statusData);
        onStatusUpdate?.(statusData);
      } else {
        setError('Статус не найден');
        setStatus(null);
      }
    } catch (err) {
      console.error('Error checking application status:', err);
      setError('Ошибка соединения с ботом');
      setStatus(null);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'success':
      case 'submitted':
        return 'text-green-600 bg-green-100';
      case 'processing':
      case 'in_progress':
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'failed':
      case 'error':
        return 'text-red-600 bg-red-100';
      case 'queued':
      case 'waiting':
        return 'text-blue-600 bg-blue-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'success':
      case 'submitted':
        return '✅';
      case 'processing':
      case 'in_progress':
        return '⚙️';
      case 'pending':
      case 'queued':
      case 'waiting':
        return '⏳';
      case 'failed':
      case 'error':
        return '❌';
      default:
        return '❓';
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-gray-700">Статус в боте</h3>
        <button
          onClick={checkStatus}
          disabled={loading}
          className="flex items-center gap-1 px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 transition-colors"
        >
          {loading ? (
            <>
              <svg className="animate-spin w-3 h-3" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Проверка...
            </>
          ) : (
            <>
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Обновить
            </>
          )}
        </button>
      </div>

      {error && (
        <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
          {error}
        </div>
      )}

      {status ? (
        <div className="space-y-2">
          <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(status.status)}`}>
            <span className="text-base">{getStatusIcon(status.status)}</span>
            {status.status}
          </div>

          {status.progress && (
            <div className="text-sm text-gray-600">
              <span className="font-medium">Прогресс:</span> {status.progress}
            </div>
          )}

          {status.message && (
            <div className="text-sm text-gray-600">
              <span className="font-medium">Сообщение:</span> {status.message}
            </div>
          )}

          {status.timestamp && (
            <div className="text-xs text-gray-500">
              Обновлен: {new Date(status.timestamp).toLocaleString('ru-RU')}
            </div>
          )}

          {/* Show additional fields from bot response */}
          {Object.entries(status)
            .filter(([key]) => !['application_id', 'status', 'message', 'progress', 'timestamp'].includes(key))
            .map(([key, value]) => (
              <div key={key} className="text-xs text-gray-500">
                <span className="font-medium capitalize">{key}:</span> {String(value)}
              </div>
            ))}
        </div>
      ) : !loading && !error && (
        <div className="text-sm text-gray-500 italic">
          Нажмите "Обновить" для проверки статуса
        </div>
      )}
    </div>
  );
}