# Vercel Deployment Guide

## Prerequisites
- Vercel account
- Neon PostgreSQL database (already set up)
- GitHub repository (recommended)

## Deployment Steps

### 1. Environment Variables
Set these environment variables in Vercel dashboard:

```bash
DATABASE_URL=your_neon_database_url
NEXTAUTH_SECRET=your_secret_key_here
NEXTAUTH_URL=https://your-domain.vercel.app
ADMIN_EMAIL=<EMAIL>
ADMIN_PHONE=+**********
WAPPI_AUTHORIZATION=270d935319ba47dcd6de2886c86e16b5055d792b
WAPPI_PROFILE_ID=cbdbe806-b360
```

### 2. Database Setup
Your database is already configured and tables are created. No additional setup needed.

### 3. Deploy to Vercel

#### Option A: GitHub Integration (Recommended)
1. Push code to GitHub repository
2. Connect GitHub repo to Vercel
3. Set environment variables in Vercel dashboard
4. Deploy automatically

#### Option B: Vercel CLI
```bash
npm install -g vercel
vercel login
vercel --prod
```

### 4. Post-Deployment Checklist
- [ ] Verify application loads correctly
- [ ] Test admin login with configured credentials
- [ ] Test visa application form submission
- [ ] Test credentials management page
- [ ] Verify database connections work
- [ ] Test application status workflow (NEW → PAID → COMPLETED)

## Important Notes

1. **Database Connection**: Uses Neon PostgreSQL with connection pooling
2. **Prisma Client**: Automatically generated during build process
3. **Admin Access**: Only configured admin email/phone can access admin panel
4. **Security**: All credentials are encrypted and securely stored

## Environment Variables Details

- `DATABASE_URL`: Your Neon PostgreSQL connection string
- `NEXTAUTH_SECRET`: Random secret for JWT signing (generate with `openssl rand -base64 32`)
- `NEXTAUTH_URL`: Your production domain (automatically set by Vercel)
- `ADMIN_EMAIL`: Email address for admin login
- `ADMIN_PHONE`: Phone number for admin login
- `WAPPI_AUTHORIZATION`: Authorization token for Wappi.pro API
- `WAPPI_PROFILE_ID`: Profile ID for Wappi.pro WhatsApp integration

## Troubleshooting

### Build Errors
- Ensure all environment variables are set
- Check Prisma client generation in build logs

### Database Connection Issues
- Verify DATABASE_URL format
- Check Neon database status
- Ensure connection pooling is enabled

### Authentication Issues
- Verify NEXTAUTH_SECRET is set
- Check NEXTAUTH_URL matches your domain
- Confirm admin credentials are correct