{"name": "italyvisa", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:submit-button": "node validate-submit-button.js", "postinstall": "prisma generate", "db:generate": "npx drizzle-kit generate", "db:migrate": "node scripts/migrate.js", "db:deploy": "npm run db:generate; npm run db:migrate", "db:deploy:win": "npm run db:generate && npm run db:migrate", "prisma:generate": "npx prisma generate", "prisma:push": "npx dotenv -e .env.local -- prisma db push", "prisma:push:simple": "npx prisma db push", "vercel-build": "prisma generate && next build"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@neondatabase/serverless": "^1.0.1", "@prisma/client": "^6.13.0", "@types/bcryptjs": "^3.0.0", "bcryptjs": "^3.0.2", "date-fns": "^4.1.0", "drizzle-orm": "^0.44.4", "next": "15.4.5", "next-auth": "^4.24.11", "prisma": "^6.13.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "babel-jest": "^30.0.5", "drizzle-kit": "^0.31.4", "eslint": "^9", "eslint-config-next": "15.4.5", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}