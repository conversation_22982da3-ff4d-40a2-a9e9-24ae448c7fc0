# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an Italy Visa Application System (italyvisa) - a Next.js application for submitting Italian visa applications with an administrative panel. The system supports both individual and family applications with a complete workflow from submission to completion.

## Technology Stack

- **Frontend**: Next.js 15 (App Router), React 19, TypeScript, Tailwind CSS
- **Database**: PostgreSQL (Neon.tech) with dual ORM setup:
  - **Drizzle ORM**: Primary database operations (`src/lib/db/schema.ts`)
  - **Prisma**: Additional schema and migrations (`prisma/schema.prisma`)
- **Authentication**: NextAuth.js with credential provider (admin-only access)
- **Forms**: React Hook Form with Zod validation
- **Testing**: Jest with Testing Library (jsdom environment)
- **Containerization**: Docker with Docker Compose

## Key Development Commands

### Development
```bash
npm run dev              # Start development server
npm run dev:turbo        # Start with Turbopack (faster)
```

### Building and Production
```bash
npm run build            # Build for production (includes Prisma generate)
npm run start            # Start production server
npm run vercel-build     # Build command for Vercel deployment
```

### Database Operations
```bash
# Drizzle (primary)
npm run db:generate      # Generate Drizzle migrations
npm run db:migrate       # Apply Drizzle migrations
npm run db:deploy        # Generate + migrate (Unix)
npm run db:deploy:win    # Generate + migrate (Windows)

# Prisma (secondary)
npm run prisma:generate  # Generate Prisma client
npm run prisma:push      # Push schema changes (development)
npm run prisma:push:simple # Push without env file
```

### Testing
```bash
npm run test                    # Run all tests
npm run test:watch             # Run tests in watch mode
npm run test:coverage          # Run tests with coverage
npm run test:submit-button     # Validate submit button logic
```

### Linting and Quality
```bash
npm run lint             # ESLint (configured to ignore build errors)
```

## Database Architecture

### Dual ORM Setup
The project uses both Drizzle and Prisma for different purposes:

- **Drizzle** (`src/lib/db/schema.ts`): Primary schema for application operations
- **Prisma** (`prisma/schema.prisma`): Extended schema with relations and VFS integration

### Key Models
- **VisaApplication**: Main application data with status workflow (NEW → PAID → COMPLETED)
- **VisaCenterCredential**: VFS Global account credentials with priority system
- **ApplicationSubmission**: Links applications to credentials for submission tracking

### Status Workflow
Applications follow a specific workflow:
1. **NEW** ("Новая"): Initial submission
2. **PAID** ("Оплаченная"): Payment confirmed, ready for VFS submission  
3. **COMPLETED** ("Завершенная"): Successfully submitted to VFS

## Authentication System

Admin-only access using NextAuth.js with credentials provider:
- Login supports both email and phone number
- Credentials configured via environment variables:
  - `ADMIN_EMAIL`
  - `ADMIN_PHONE` 
  - `ADMIN_PASSWORD`
- Protected routes use session verification
- Sign-in page: `/auth/signin`

## Form Validation

All forms use React Hook Form with Zod validation schemas in `src/lib/validation.ts`:
- **Individual applications**: `visaApplicationSchema`
- **Family applications**: `applicantSchema` (array-based)
- Strict validation for passport numbers, names (Latin uppercase), and contact info
- Real-time validation feedback

## API Structure

### Main Endpoints
- `POST /api/applications` - Create new application
- `GET /api/applications` - List all applications (admin)  
- `PATCH /api/applications/[id]` - Update application status
- `POST /api/applications/[id]` - Get JSON payload for VFS submission

### Credentials Management
- `GET /api/credentials` - List VFS credentials
- `POST /api/credentials` - Create new credential
- `GET /api/credentials/best-available` - Get optimal credential for submission
- `PATCH /api/credentials/[id]` - Update credential

## Component Architecture

### Key Components
- **VisaApplicationForm**: Individual application form
- **FamilyApplicationForm**: Multi-applicant family form with dynamic fields
- **AdminPanel/AdminPanelV2**: Application management interface
- **CredentialsManager/CredentialsManagerV2**: VFS account management
- **ApplicationModal/ApplicationModalV2**: Detailed application view

### Important Implementation Notes
- Submit buttons use `disabled={isSubmitting || !isValid}` logic
- Forms auto-populate `fullName` from individual name fields
- Family forms support up to 5 applicants with relationship tracking
- All forms include VFS-specific field mappings

## Environment Configuration

### Required Variables
```bash
DATABASE_URL=postgresql://...
NEXTAUTH_SECRET=your-secret
NEXTAUTH_URL=http://localhost:3000
ADMIN_EMAIL=<EMAIL>
ADMIN_PHONE=+**********
ADMIN_PASSWORD=admin123
```

### Optional (VFS Integration)
```bash
WAPPI_AUTHORIZATION=token
WAPPI_PROFILE_ID=profile-id
```

## Docker Setup

- **Dockerfile**: Multi-stage build with standalone output
- **docker-compose.yml**: Includes PostgreSQL and Adminer services
- Default ports: App (3000), PostgreSQL (5432), Adminer (8080)

## Testing Configuration

- **Jest**: Configured for jsdom environment with module aliases
- **Setup**: `jest.setup.js` with Testing Library extensions  
- **Coverage**: Collects from `src/**/*.{js,jsx,ts,tsx}`
- **Paths**: Uses `@/` alias for `src/` directory

## Build Configuration

### Next.js Config (`next.config.ts`)
- **Output**: Standalone (optimized for Docker/serverless)
- **Build Options**: Ignores ESLint and TypeScript errors during builds
- **Environment**: Production-ready configuration

### TypeScript
- Modern configuration with strict type checking
- Path aliases configured for `@/` → `src/`

## Development Workflow

1. **Database First**: Apply migrations before code changes
2. **Form Testing**: Use `npm run test:submit-button` to validate form logic  
3. **Docker Testing**: Use `docker-compose up --build` for full-stack testing
4. **Validation**: Always run `npm run lint` and `npm run test` before commits

## Deployment (Vercel)

The application is configured for Vercel deployment:
- **Build Command**: `vercel-build` (includes Prisma generation)
- **Environment**: All required variables must be configured
- **Database**: Uses Neon PostgreSQL with connection pooling
- **Monitoring**: Includes comprehensive error handling and logging