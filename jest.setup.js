require('@testing-library/jest-dom')

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter() {
    return {
      route: '/',
      pathname: '/',
      query: {},
      asPath: '/',
      push: jest.fn(),
      pop: jest.fn(),
      reload: jest.fn(),
      back: jest.fn()
    }
  },
}))

// Mock fetch globally
global.fetch = jest.fn()

// Reset fetch mock before each test
beforeEach(() => {
  fetch.mockClear()
})
